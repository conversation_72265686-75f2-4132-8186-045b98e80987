package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for Iller entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Mahkeme Karar Tipi")
public class MahkemeKararTipiDTO {

    private Long kararKodu; //100, 200 gibi predefined kodlar

    private String kararTipi; // ÖNLEYİCİ HAKİM KARARI gibi

    private String kararTuru; //ADLI, ONLEYICI

    private Long sonlandirma; //Bu tip sonlandirma mi?

}
