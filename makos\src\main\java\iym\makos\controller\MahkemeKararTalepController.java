package iym.makos.controller;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.Response;
import iym.common.util.CommonUtils;
import iym.common.util.HashUtils;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.mktalep.requestprocessor.service.MahkemeKararProcessService;
import iym.makos.domain.mktalep.stateupdater.service.MkTalepStateUpdaterService;
import iym.makos.model.MahkemeKararTalepBilgisiRequest;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.query.*;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.*;
import iym.makos.model.dto.mktalep.request.it.ITKararRequest;
import iym.makos.model.dto.mktalep.request.it.ITKararResponse;
import iym.makos.model.dto.mktalep.update.MahkemeKararTalepStateUpdateRequest;
import iym.makos.model.dto.mktalep.update.MahkemeKararTalepStateUpdateResponse;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepIslenecekView;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepSorguView;
import iym.makos.model.dto.view.IDMahkemeKarariInfo;
import iym.makos.service.file.FilePersisterService;
import iym.makos.service.mktalep.MahkemeKararTalepService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/mahkemeKararTalep")
@Slf4j
public class MahkemeKararTalepController {

    public final static String MAHKEME_KARAR_FILE_PART = "mahkemeKararDosyasi";
    public final static String MAHKEME_KARAR_DETAY_JSON_PART = "mahkemeKararDetay";

    @Autowired
    private MahkemeKararTalepService mahkemeKararTalepService;

    @Autowired
    private MahkemeKararProcessService mahkemeKararProcessService;

    @Autowired
    private MkTalepStateUpdaterService mkTalepStateUpdaterService;

    @Autowired
    private FilePersisterService filePersisterService;

    /* ****************************************************************************/
    /* ********************* MULTIPART OPERATIONS START HERE **********************/
    /* ****************************************************************************/

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/yeniKararID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/yeniKararID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<IDYeniKararResponse> yeniKararID(
            @Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiID,
            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDYeniKararRequest.class))
            @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) IDYeniKararRequest request,
            Authentication authentication) {

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            if (log.isDebugEnabled())
                log.debug("IDYeniKararRequest received. request:{}, user:{}, file:{}, md5:{}", request, user.getUsername(), mahkemeKararDosyasiID.getOriginalFilename(), HashUtils.md5Checksum(mahkemeKararDosyasiID));
            else
                log.info("IDYeniKararRequest received. id:{}, user:{}, file:{}, md5:{}", request.getId(), user.getUsername(), mahkemeKararDosyasiID.getOriginalFilename(), HashUtils.md5Checksum(mahkemeKararDosyasiID));

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiID);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("IDYeniKararRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("IDYeniKararRequest file save failed. id:{}", request.getId());
                IDYeniKararResponse response = IDYeniKararResponse.builder()
                        .requestId(request.getId())
                        .response(saveResponse)
                        .build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            IDYeniKararResponse response = mahkemeKararProcessService.process(request, IDYeniKararResponse.class, MakosUserDetails.fromUserDetails(user));
            if (response.getResponse().getResponseCode() == MakosResponseCode.SUCCESS)
                log.info("IDYeniKararRequest processed (success), id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            else
                log.error("IDYeniKararRequest processed (failed), id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);

            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode())).body(response);
        } catch (Exception e) {
            log.error("IDYeniKararRequest failed. id:{}", request.getId(), e);
            IDYeniKararResponse response = IDYeniKararResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/uzatmaKarariID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/uzatmaKarariID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDUzatmaKarariResponse> uzatmaKarariID(
            @Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiID,
            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDUzatmaKarariRequest.class))
            @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) IDUzatmaKarariRequest request,
            Authentication authentication
    ) {
        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            log.info("IDUzatmaKarariRequest received. requestId:{}, user:{}", request.getId(), user.getUsername());

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiID);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("IDUzatmaKarariRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("IDUzatmaKarariRequest file save failed. id:{}", request.getId());
                IDUzatmaKarariResponse response = IDUzatmaKarariResponse.builder()
                        .requestId(request.getId())
                        .response(saveResponse)
                        .build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            IDUzatmaKarariResponse response = mahkemeKararProcessService.process(request, IDUzatmaKarariResponse.class, MakosUserDetails.fromUserDetails(user));
            if (response.getResponse().getResponseCode() == MakosResponseCode.SUCCESS)
                log.info("IDUzatmaKarariRequest processed (success), id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            else
                log.error("IDUzatmaKarariRequest processed (failed), id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);

            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode())).body(response);

        } catch (Exception e) {
            log.error("IDYeniKararRequest failed. id:{}", request.getId(), e);
            IDUzatmaKarariResponse response = IDUzatmaKarariResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/sonlandirmaKarariID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/sonlandirmaKarariID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDSonlandirmaKarariResponse> sonlandirmaKarariID(
            @Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiID,
            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDSonlandirmaKarariRequest.class))
            @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) IDSonlandirmaKarariRequest request,
            Authentication authentication
    ) {
        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            log.info("IDSonlandirmaKarariRequest received:{}, user:{}", request, user.getUsername());

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiID);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("IDSonlandirmaKarariRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("IDSonlandirmaKarariRequest file save failed. id:{}", request.getId());
                IDSonlandirmaKarariResponse response = IDSonlandirmaKarariResponse.builder()
                        .requestId(request.getId())
                        .response(saveResponse)
                        .build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            IDSonlandirmaKarariResponse response = mahkemeKararProcessService.process(request, IDSonlandirmaKarariResponse.class, MakosUserDetails.fromUserDetails(user));
            log.info("IDSonlandirmaKarariRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDSonlandirmaKarariRequest failed. id:{}", request.getId(), e);
            IDSonlandirmaKarariResponse response = IDSonlandirmaKarariResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/kararGonderID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/yenikararIT", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ITKararResponse> yenikararIT(
            @Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiIT,
            @Parameter(required = true, description = "IT Mahkeme Karar Detaylari", schema = @Schema(implementation = ITKararRequest.class))
            @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) ITKararRequest request,
            Authentication authentication
    ) {

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            log.info("ITKararRequest received:{}, user:{}", request, user.getUsername());

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiIT);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("ITKararRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("ITKararRequest file save failed. id:{}", request.getId());
                ITKararResponse response = ITKararResponse.builder()
                        .requestId(request.getId())
                        .response(saveResponse)
                        .build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            ITKararResponse response = mahkemeKararProcessService.process(request, ITKararResponse.class, MakosUserDetails.fromUserDetails(user));
            log.info("ITKararRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("ITKararRequest failed. id:{}", request.getId(), e);
            ITKararResponse response = ITKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping(path = "/aidiyatBilgisiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDAidiyatBilgisiGuncellemeResponse> aidiyatBilgisiGuncelle(
            @Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiID,
            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDAidiyatBilgisiGuncellemeRequest.class))
            @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) IDAidiyatBilgisiGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            log.info("IDAidiyatBilgisiGuncellemeRequest received:{}, user:{}", request, user.getUsername());

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiID);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("IDAidiyatBilgisiGuncellemeRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("IDAidiyatBilgisiGuncellemeRequest file save failed. id:{}", request.getId());
                IDAidiyatBilgisiGuncellemeResponse response = IDAidiyatBilgisiGuncellemeResponse.builder()
                        .requestId(request.getId())
                        .response(saveResponse)
                        .build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            IDAidiyatBilgisiGuncellemeResponse response = mahkemeKararProcessService.process(request, IDAidiyatBilgisiGuncellemeResponse.class, MakosUserDetails.fromUserDetails(user));
            log.info("IDAidiyatBilgisiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDAidiyatBilgisiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDAidiyatBilgisiGuncellemeResponse response = IDAidiyatBilgisiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping(path = "/sucTipiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDSucTipiGuncellemeResponse> sucTipiGuncelle(
            @Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDSucTipiGuncellemeRequest.class))
            @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) IDSucTipiGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            log.info("IDSucTipiGuncellemeRequest received:{}, user:{}", request, user.getUsername());

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiID);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("IDSucTipiGuncellemeRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("IDSucTipiGuncellemeRequest file save failed. id:{}", request.getId());
                IDSucTipiGuncellemeResponse response = IDSucTipiGuncellemeResponse.builder()
                        .requestId(request.getId())
                        .response(saveResponse)
                        .build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            IDSucTipiGuncellemeResponse response = mahkemeKararProcessService.process(request, IDSucTipiGuncellemeResponse.class, MakosUserDetails.fromUserDetails(user));
            log.info("IDSucTipiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDSucTipiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDSucTipiGuncellemeResponse response = IDSucTipiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping(path = "/hedefBilgisiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDHedefGuncellemeResponse> hedefBilgisiGuncelle(
            @Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiID,
            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDHedefGuncellemeRequest.class))
            @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) IDHedefGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            log.info("IDHedefBilgiGuncellemeRequest received:{}, user:{}", request, user.getUsername());

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiID);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("IDHedefBilgiGuncellemeRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("IDHedefBilgiGuncellemeRequest file save failed. id:{}", request.getId());
                IDHedefGuncellemeResponse response = IDHedefGuncellemeResponse.builder()
                        .requestId(request.getId())
                        .response(saveResponse)
                        .build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            IDHedefGuncellemeResponse response = mahkemeKararProcessService.process(request, IDHedefGuncellemeResponse.class, MakosUserDetails.fromUserDetails(user));
            log.info("IDHedefBilgiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDHedefBilgiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDHedefGuncellemeResponse response = IDHedefGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    /* ****************************************************************************/
    /* ********************* MULTIPART OPERATIONS END HERE ************************/
    /* ****************************************************************************/

    @PostMapping(path = "/mahkemeBilgisiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDMahkemeKararGuncellemeResponse> mahkemeBilgisiGuncelle(
            @Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_FILE_PART) MultipartFile mahkemeKararDosyasiID,
            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDMahkemeKararGuncellemeRequest.class))
            @NotNull @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_JSON_PART) IDMahkemeKararGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();

            log.info("IDMahkemeBilgiGuncellemeRequest received:{}, user:{}", request, user.getUsername());

            MakosApiResponse saveResponse = saveRequestFile(request, mahkemeKararDosyasiID);
            if (saveResponse.getResponseCode() == MakosResponseCode.SUCCESS) {
                log.info("IDMahkemeBilgiGuncellemeRequest file saved. id:{}, path:{}", request.getId(), saveResponse.getResponseMessage());
                request.setFileName(saveResponse.getResponseMessage());
            } else {
                log.error("IDMahkemeBilgiGuncellemeRequest file save failed. id:{}", request.getId());
                IDMahkemeKararGuncellemeResponse response = IDMahkemeKararGuncellemeResponse.builder()
                        .requestId(request.getId())
                        .response(saveResponse)
                        .build();
                return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
            }

            IDMahkemeKararGuncellemeResponse response = mahkemeKararProcessService.process(request, IDMahkemeKararGuncellemeResponse.class, MakosUserDetails.fromUserDetails(user));
            log.info("IDMahkemeBilgiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDMahkemeBilgiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDMahkemeKararGuncellemeResponse response = IDMahkemeKararGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/islenecekKararListele")
    public ResponseEntity<MahkemeKararTalepIslenecekResponse> islenecekKararListele(
            @NotNull @Valid @RequestBody MahkemeKararTalepIslenecekRequest request, Authentication authentication) {
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            String kurumKodu = user.getKullaniciKurum().getValue();
            Response<List<MahkemeKararTalepIslenecekView>> islenecekKararListesiResponse = mahkemeKararTalepService.islenecekMahkemeKararTalepleri(kurumKodu);
            if (!islenecekKararListesiResponse.isSuccess()) {
                return ResponseEntity.ok(MahkemeKararTalepIslenecekResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.FAILED)
                                .responseMessage("İşlenecek Karar Listesi Başarısız : " + islenecekKararListesiResponse.getResultDetails())
                                .build())
                        .build());
            }

            List<MahkemeKararTalepIslenecekView> islenecekKararListesi = islenecekKararListesiResponse.getResult();

            return ResponseEntity.ok(MahkemeKararTalepIslenecekResponse.builder()
                    .islenecekKararlar(islenecekKararListesi)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("islenecekKararlariListele process failed, requestId:{}", request.hashCode(), ex);
            return ResponseEntity.ok(MahkemeKararTalepIslenecekResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("İşlenecek Karar Listesi Başarısız")
                            .build())
                    .build());
        }
    }

    @PostMapping("/mahkemeKararTalepSorgu")
    public ResponseEntity<IDMahkemeKararTalepSorgulamaResponse> mahkemeKararTalepSorgu(
            @NotNull @Valid @RequestBody IDMahkemeKararTalepSorgulamaRequest sorguParam, Authentication authentication) {
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            String kurumKodu = user.getKullaniciKurum().getValue();
            List<MahkemeKararTalepSorguView> sonucListesi = mahkemeKararTalepService.mahkemeKararTalepSorgu(kurumKodu, sorguParam);

            return ResponseEntity.ok(IDMahkemeKararTalepSorgulamaResponse.builder()
                    .mahkemeKararTalepSorguViewListesi(sonucListesi)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("mahkemeKararTalepSorgu process failed, requestId:{}", sorguParam.hashCode(), ex);
            IDMahkemeKararTalepSorgulamaResponse response = IDMahkemeKararTalepSorgulamaResponse.builder()
                    .mahkemeKararTalepSorguViewListesi(null)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Sorgu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }


    @PostMapping("talepGuncelle")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_TEMSILCISI')")
    private ResponseEntity<MahkemeKararTalepStateUpdateResponse> talepGuncelle(
            @NotNull @Valid @RequestBody MahkemeKararTalepStateUpdateRequest request, Authentication authentication) {
        log.debug("TalepGuncelle request received. id:{}", request.getId());

        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();
            log.info("TalepGuncelle Request received:{}, user:{}", request, user.getUsername());

            MahkemeKararTalepStateUpdateResponse response = mkTalepStateUpdaterService.process(request, MakosUserDetails.fromUserDetails(user));
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception ex) {
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            MahkemeKararTalepStateUpdateResponse response = MahkemeKararTalepStateUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping("/mahkemeKararTalepBilgisi")
    public ResponseEntity<MahkemeKararTalepQueryResponse> mahkemeKararTalepBilgisi(
            @NotNull @Valid @RequestBody MahkemeKararTalepBilgisiRequest sorguParam, Authentication authentication) {
        try {
            UserDetails user = (UserDetails) authentication.getPrincipal();
            log.info("MahkemeKararTalep Detay Bilgi Sorgusu Received:{}, user:{}", sorguParam, user.getUsername());

            Response<IDMahkemeKarariInfo> mahkemeKarariInfoResp = mahkemeKararTalepService.getMahkemeKararTalepDetails(sorguParam.getMahkemeKararId(), true);
            log.info("MahkemeKararTalep Detay Bilgi Sorgusu Result:{}", mahkemeKarariInfoResp);
            if (mahkemeKarariInfoResp.isSuccess()) {
                return ResponseEntity.ok(MahkemeKararTalepQueryResponse.builder()
                        .iDMahkemeKarariInfo(mahkemeKarariInfoResp.getResult())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.SUCCESS)
                                .build())
                        .build());
            } else {
                return ResponseEntity.ok(MahkemeKararTalepQueryResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.FAILED)
                                .responseMessage("MahkemeKararTalep Detay Bilgi Sorgusu Başarısız: ")
                                .build())
                        .build());
            }


        } catch (Exception ex) {
            log.error("MahkemeKararTalep Detay Bilgi Sorgusu failed, requestId:{}", sorguParam.hashCode(), ex);
            MahkemeKararTalepQueryResponse response = MahkemeKararTalepQueryResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("MahkemeKararTalep Detay Bilgi Sorgusu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }


    public String getFullEvrakSavePath(MkTalepRequest request, String fileName) {
        String folderName = request.getId().toString();
        String baseFolder = CommonUtils.appendSubFolder(CommonUtils.getEvrakBasePath(), folderName);
        return CommonUtils.appendFileToPath(baseFolder, fileName);
    }

    public MakosApiResponse saveRequestFile(MkTalepRequest request, MultipartFile file){
        String filePath = null;
        try{
            // TODO update here if necessary
            filePath = getFullEvrakSavePath(request, file.getOriginalFilename());
            boolean saved = filePersisterService.saveFileToDisk(filePath, file.getBytes());
            if (saved) {
                return MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.SUCCESS)
                        .responseMessage(filePath)
                        .build();
            } else {
                return MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.FAILED)
                        .responseMessage("Dosya saklama hatasi")
                        .build();
            }
        }catch (Exception e){
            log.error("saveRequestFile failed. id:{}, filePath:{}", request.getId(), filePath, e);
            return MakosApiResponse.builder()
                    .responseCode(MakosResponseCode.FAILED)
                    .responseMessage("Dosya saklama hatasi")
                    .build();
        }
    }

}