package iym.makos.mapper;

import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguInfo;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepIslenecekView;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepSorguView;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeKararTalep entity and DTO
 */
@Component
public class MahkemeKararTalepSorguMapper {

    public MahkemeKararTalepIslenecekView toKararTalepViewInfo(MahkemeKararTalepSorguInfo entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeKararTalepIslenecekView.builder()
                .mahkemeKararTalepId(entity.getId())
                .evrakId(entity.getEvrakId())
                .kaydedenKullaniciId(entity.getKaydedenKullaniciId())
                .kararKayitTarihi(entity.getKayitTarihi())
                .durumu(entity.getDurum())
                .mahkemeKararNo(entity.getMahkemeKararNo())
                .sorusturmaNo(entity.getSorusturmaNo())
                //.mahkemeIlIlceKodu(entity.geti)
                .aciklama(entity.getAciklama())
                .build();
    }

    public MahkemeKararTalepSorguView toMahkemeKararTalepSorguView(MahkemeKararTalepSorguInfo entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeKararTalepSorguView.builder()
                .mahkemeKararTalepId(entity.getId())
                .evrakId(entity.getEvrakId())
                .evrakSiraNo(entity.getEvrakSiraNo())
                .evrakNo(entity.getEvrakNo())
                .kararKayitTarihi(entity.getKayitTarihi())
                .kaydedenKullaniciId(entity.getKaydedenKullaniciId())
                .kaydedenKullaniciAdi(entity.getKullaniciAdi())
                .kaydedenAdiSoyadi(entity.getAdi() + " " + entity.getSoyadi())
                .durumu(entity.getDurum())
                .mahkemeKararNo(entity.getMahkemeKararNo())
                .sorusturmaNo(entity.getSorusturmaNo())
                .aciklama(entity.getAciklama())
                .mahkemeKodu(entity.getMahkemeKodu())
                .mahkemeAdi(entity.getMahkemeAdi())
                .kurumKodu(entity.getKurumKodu())
                .kurumAdi(entity.getKurumAdi())
                .evrakKonusu(entity.getEvrakKonusu())
                .build();
    }


}
