package iym.makos.model.dto.mktalep.request.it;

import iym.makos.model.dto.mktalep.MkTalepResponse;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Data
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Jacksonized
public class ITKararResponse extends MkTalepResponse {

  @NotNull
  private Long evrakId;

  @NotNull
  private Long itkTalepId;

}

