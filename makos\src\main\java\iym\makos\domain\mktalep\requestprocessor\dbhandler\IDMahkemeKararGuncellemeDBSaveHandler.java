package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararGuncellemeTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararGuncelleTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.MahkemeKararGuncellemeAlanTuru;
import iym.makos.model.api.MahkemeKararGuncellemeBilgi;
import iym.makos.model.api.MahkemeKararGuncellemeDetay;
import iym.makos.model.dto.mktalep.request.id.GenelEvrakRequest;
import iym.makos.model.dto.mktalep.request.id.IDMahkemeKararGuncellemeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IDMahkemeKararGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDMahkemeKararGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    private final DbHedeflerTalepService dbHedeflerTalepService;
    private final DbHedeflerService dbHedeflerService;
    private final DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;
    private final KararRequestMapper kararRequestMapper;
    private final DbMahkemeKararGuncelleTalepService dbMahkemeKararGuncelleTalepService;

    @Autowired
    public IDMahkemeKararGuncellemeDBSaveHandler(DbMahkemeKararService dbMahkemeKararService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService
            , DbHedeflerTalepService dbHedeflerTalepService
            , DbHedeflerService dbHedeflerService
            , DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService
            , KararRequestMapper kararRequestMapper
            , DbMahkemeKararGuncelleTalepService dbMahkemeKararGuncelleTalepService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
        this.dbHedeflerTalepService = dbHedeflerTalepService;
        this.dbHedeflerService = dbHedeflerService;
        this.dbHedeflerAidiyatTalepService = dbHedeflerAidiyatTalepService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbMahkemeKararGuncelleTalepService = dbMahkemeKararGuncelleTalepService;
    }

    @Override
    @Transactional
    public void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, IDMahkemeKararGuncellemeRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {
        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();

        for (MahkemeKararGuncellemeDetay mahkemeKararGuncellemeDetay : request.getMahkemeKararGuncellemeDetayListesi()) {

            //Güncellemeye konu mahkeme karari bul
            MahkemeKararDetay ilgiliMahhemeKararDetay = mahkemeKararGuncellemeDetay.getMahkemeKararDetay();
            Optional<MahkemeKarar> mahkemeKararOpt = dbMahkemeKararService.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                    , ilgiliMahhemeKararDetay.getMahkemeKodu()
                    , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                    , ilgiliMahhemeKararDetay.getSorusturmaNo());
            if (mahkemeKararOpt.isEmpty()) {
                String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu(), ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                throw new MakosResponseException(errorStr);
            } else {
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();

                DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

                String yeniMahkemeKodu = "";
                String yeniSorusturmaNo = "";
                String yeniMahkemeKararNo = "";

                for (MahkemeKararGuncellemeBilgi mahkemeKararGuncellemeBilgi : mahkemeKararGuncellemeDetay.getMahkemeKararGuncellemeBilgiListesi()) {
                    if (mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlanTuru() == MahkemeKararGuncellemeAlanTuru.MAHKEME_KODU) {
                        yeniMahkemeKodu = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                    } else if (mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlanTuru() == MahkemeKararGuncellemeAlanTuru.MAHKEME_KARAR_NO) {
                        yeniMahkemeKararNo = mahkemeKararGuncellemeBilgi.getYeniDegeri();

                    } else if (mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlanTuru() == MahkemeKararGuncellemeAlanTuru.SORUSTURMA_NO) {
                        yeniSorusturmaNo = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                    }
                }


                if (CommonUtils.isNullOrEmpty(yeniMahkemeKodu) && CommonUtils.isNullOrEmpty(yeniSorusturmaNo) & CommonUtils.isNullOrEmpty(yeniMahkemeKararNo)) {
                    throw new MakosResponseException("mahkemeKodu, soruşturma numarası veya  makhkeme karar numaralarımdan en az bir tanesi belirtilmeldir.");
                }

                String updateColumnNames = mahkemeKararGuncellemeDetay.getMahkemeKararGuncellemeBilgiListesi().stream()
                        .map(guncellemeBilgi -> guncellemeBilgi.getMahkemeKararGuncellemeAlanTuru().name())
                        .collect(Collectors.joining(","));


                MahkemeKararGuncellemeTalep mahkemeKararGuncellemeTalep = new MahkemeKararGuncellemeTalep();
                mahkemeKararGuncellemeTalep.setDetayMahkemeKararTalepId(savedDMahkemeKararTalep.getId());
                mahkemeKararGuncellemeTalep.setMahkemeKodu(yeniMahkemeKodu);
                mahkemeKararGuncellemeTalep.setSorusturmaNo(yeniSorusturmaNo);
                mahkemeKararGuncellemeTalep.setMahkemeKararNo(yeniMahkemeKararNo);
                mahkemeKararGuncellemeTalep.setUpdateColumnNames(updateColumnNames);

                MahkemeKararGuncellemeTalep savedMahkemeKararGuncellemeTalep = dbMahkemeKararGuncelleTalepService.save(mahkemeKararGuncellemeTalep);
                if (savedMahkemeKararGuncellemeTalep == null) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_MAHKEMEBILGIGUNCELLEME_KAYIT_HATASI);
                }
            }
        }
    }

    @Transactional
    public Long kaydet1(IDMahkemeKararGuncellemeRequest request, LocalDateTime kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = null;//mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = dbMahkemeKararTalepService.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            for (MahkemeKararGuncellemeDetay mahkemeKararGuncellemeDetay : request.getMahkemeKararGuncellemeDetayListesi()) {

                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = mahkemeKararGuncellemeDetay.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = dbMahkemeKararService.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu(), ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                } else {
                    MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();

                    DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
                    DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

                    String yeniMahkemeKodu = "";
                    String yeniSorusturmaNo = "";
                    String yeniMahkemeKararNo = "";

                    for (MahkemeKararGuncellemeBilgi mahkemeKararGuncellemeBilgi : mahkemeKararGuncellemeDetay.getMahkemeKararGuncellemeBilgiListesi()) {
                        if (mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlanTuru() == MahkemeKararGuncellemeAlanTuru.MAHKEME_KODU) {
                            yeniMahkemeKodu = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                        } else if (mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlanTuru() == MahkemeKararGuncellemeAlanTuru.MAHKEME_KARAR_NO) {
                            yeniMahkemeKararNo = mahkemeKararGuncellemeBilgi.getYeniDegeri();

                        } else if (mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlanTuru() == MahkemeKararGuncellemeAlanTuru.SORUSTURMA_NO) {
                            yeniSorusturmaNo = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                        }
                    }


                    if (CommonUtils.isNullOrEmpty(yeniMahkemeKodu) && CommonUtils.isNullOrEmpty(yeniSorusturmaNo) & CommonUtils.isNullOrEmpty(yeniMahkemeKararNo)) {
                        throw new MakosResponseException("mahkemeKodu, soruşturma numarası veya  makhkeme karar numaralarımdan en az bir tanesi belirtilmeldir.");
                    }

                    String updateColumnNames = mahkemeKararGuncellemeDetay.getMahkemeKararGuncellemeBilgiListesi().stream()
                            .map(guncellemeBilgi -> guncellemeBilgi.getMahkemeKararGuncellemeAlanTuru().name())
                            .collect(Collectors.joining(","));


                    MahkemeKararGuncellemeTalep mahkemeKararGuncellemeTalep = new MahkemeKararGuncellemeTalep();
                    mahkemeKararGuncellemeTalep.setDetayMahkemeKararTalepId(savedDMahkemeKararTalep.getId());
                    mahkemeKararGuncellemeTalep.setMahkemeKodu(yeniMahkemeKodu);
                    mahkemeKararGuncellemeTalep.setSorusturmaNo(yeniSorusturmaNo);
                    mahkemeKararGuncellemeTalep.setMahkemeKararNo(yeniMahkemeKararNo);
                    mahkemeKararGuncellemeTalep.setUpdateColumnNames(updateColumnNames);

                    MahkemeKararGuncellemeTalep savedMahkemeKararGuncellemeTalep = dbMahkemeKararGuncelleTalepService.save(mahkemeKararGuncellemeTalep);
                    if (savedMahkemeKararGuncellemeTalep == null) {
                        throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_MAHKEMEBILGIGUNCELLEME_KAYIT_HATASI);
                    }
                }
            }

            return mahkemeKararTalepId;
        } catch (MakosResponseException ex) {
            // Let MakosResponseException propagate without wrapping
            throw ex;
        } catch (Exception ex) {
            log.error("IDMahkemeKararGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

