package iym.makos.domain.mktaleprequest.dbhandler;

import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.GenelEvrakDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.IDYeniKararDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.dto.mktalep.request.id.GenelEvrakRequest;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.Collections;

import static iym.makos.domain.testdata.TestDataBuilder.createValidGenelEvrakRequest;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

/**
 * Unit tests for GenelEvrakDBSaveHandler.
 * <p>
 * Tests the DB handler implementation for general evrak save operations.
 * Verifies database save operations and error handling scenarios.
 *
 * <AUTHOR> Team
 */
@DisplayName("GenelEvrakDBSaveHandler Unit Tests")
class GenelEvrakDBSaveHandlerTest extends BaseDomainUnitTest {

    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;

    @InjectMocks
    private GenelEvrakDBSaveHandler dbSaveHandler;

    private MahkemeKararTalepIdWithEvrakId talepIdWithEvrakId;
    private GenelEvrakRequest request;
    private LocalDateTime kayitTarihi;
    private Long kullaniciId;


    @BeforeEach
    void setUp() {
        talepIdWithEvrakId = new MahkemeKararTalepIdWithEvrakId(1L, 2L);
        request = new GenelEvrakRequest();

        kayitTarihi = LocalDateTime.now();
        kullaniciId = 42L;


        talepIdWithEvrakId = createTestMahkemeKararTalepIdWithEvrakId();

    }

    @Test
    void advanceHandleDbSave_nullTalep_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.advanceHandleDbSave(null, request, kayitTarihi, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("mahkemeKarar/EvrakId boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullRequest_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.advanceHandleDbSave(talepIdWithEvrakId, null, kayitTarihi, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("request boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullKayitTarihi_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.advanceHandleDbSave(talepIdWithEvrakId, request, null, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kayitTarihi boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullKullaniciId_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.advanceHandleDbSave(talepIdWithEvrakId, request, kayitTarihi, null))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kullaniciId boş olamaz");
    }




}
