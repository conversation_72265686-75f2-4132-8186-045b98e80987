package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.*;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerAidiyatTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.IDMahkemeKararGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.IDSonlandirmaKarariRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDSonlandirmaKarariDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDSonlandirmaKarariRequest> {
    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    private final DbHedeflerTalepService dbHedeflerTalepService;
    private final DbHedeflerDetayTalepService dbHedeflerDetayTalepService;
    private final DbHedeflerService dbHedeflerService;
    private final DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;
    private final KararRequestMapper kararRequestMapper;


    @Autowired
    public IDSonlandirmaKarariDBSaveHandler(DbMahkemeKararService dbMahkemeKararService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService
            , DbHedeflerTalepService dbHedeflerTalepService
            , DbHedeflerDetayTalepService dbHedeflerDetayTalepService
            , DbHedeflerService dbHedeflerService
            , DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService
            , KararRequestMapper kararRequestMapper
    ) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
        this.dbHedeflerTalepService = dbHedeflerTalepService;
        this.dbHedeflerDetayTalepService = dbHedeflerDetayTalepService;
        this.dbHedeflerService = dbHedeflerService;
        this.dbHedeflerAidiyatTalepService = dbHedeflerAidiyatTalepService;
        this.kararRequestMapper = kararRequestMapper;
    }


    @Override
    @Transactional
    public void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, IDSonlandirmaKarariRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {

        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();

        for (IDHedefDetay iDHedefDetay : request.getHedefDetayListesi()) {
            String hedefNo = iDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefNo();
            HedefTip hedefTipi = iDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefTip();

            //Uzatmaya konu mahkeme karari bul
            MahkemeKararDetay ilgiliMahhemeKararDetay = iDHedefDetay.getIlgiliMahkemeKararDetayi();
            Optional<MahkemeKarar> mahkemeKararOpt = dbMahkemeKararService.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                    , ilgiliMahhemeKararDetay.getMahkemeKodu()
                    , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                    , ilgiliMahhemeKararDetay.getSorusturmaNo());

            if (mahkemeKararOpt.isEmpty()) {
                String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI
                        , ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                throw new MakosResponseException(errorStr);
            }

            MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


            //Uzatma Hedefi uzatmaya konu karar listesinin hedeflerinde var mi?
            Optional<Hedefler> iliskiliHedefOpt = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId()
                    , hedefNo, hedefTipi.getHedefKodu());
            if (iliskiliHedefOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
            }
            Hedefler iliskiliHedef = iliskiliHedefOpt.get();

            /*TODO: Eski sistemde  DetayMahkemeKararTalep ve HedeflerTalep birlike kaydediliyor ama
            aralarinda bir iliski kurulamiyor. HedeflerTalep kapatmaId'den gidilerek iliskili mahkeme karar bulunuyor
            fakat bu da sorunlara sebebiyet veriyor. bu DetayMahkemeKararTalep ile HedeflerTalep arasinda iliski kurmak icin
            HedeflerDetayTalep kaydediyoruz.
             **/
            DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
            DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

            HedeflerDetayTalep hedeflerDetayTalep = kararRequestMapper.toHedeflerDetayTalep(iDHedefDetay, mahkemeKararTalepId, iliskiliHedef.getId(), kullaniciId, kayitTarihi);
            hedeflerDetayTalep.setDetayMahkemeKararTalepId(savedDMahkemeKararTalep.getId());
            HedeflerDetayTalep savedHedeflerDetayTalep = dbHedeflerDetayTalepService.save(hedeflerDetayTalep);
            if(savedHedeflerDetayTalep == null){
                throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_HEDEFLER_DETAY_KAYDETMEHATASI, hedefNo);
            }

            HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(iDHedefDetay, mahkemeKararTalepId, kullaniciId, kayitTarihi);
            hedeflerTalepEntity.setKapatmaKararId(mahkemeKararOpt.get().getId());

            HedeflerTalep savedHedeflerTalep = dbHedeflerTalepService.save(hedeflerTalepEntity);
            //Hedef Aidiyat Talepleri Kaydet
            if (iDHedefDetay.getHedefAidiyatKodlari() != null) {
                saveHedeflerTalepAidiyatListesi(iDHedefDetay.getHedefAidiyatKodlari()
                        , savedHedeflerTalep.getId()
                        , savedHedeflerTalep.getHedefNo()
                        , kayitTarihi
                        , kullaniciId);
            }
        }

    }


        //@Override
    @Transactional
    public Long kaydet1(IDSonlandirmaKarariRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {

        Long mahkemeKararTalepId = null;//mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
        if (mahkemeKararTalepId == null) {
            throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
        }
        Long evrakId = null;

        for (IDHedefDetay iDHedefDetay : request.getHedefDetayListesi()) {
            String hedefNo = iDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefNo();
            HedefTip hedefTipi = iDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefTip();

            //Uzatmaya konu mahkeme karari bul
            MahkemeKararDetay ilgiliMahhemeKararDetay = iDHedefDetay.getIlgiliMahkemeKararDetayi();
            Optional<MahkemeKarar> mahkemeKararOpt = dbMahkemeKararService.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                    , ilgiliMahhemeKararDetay.getMahkemeKodu()
                    , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                    , ilgiliMahhemeKararDetay.getSorusturmaNo());

            if (mahkemeKararOpt.isEmpty()) {
                String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI
                        , ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                throw new MakosResponseException(errorStr);
            }

            MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


            //Uzatma Hedefi uzatmaya konu karar listesinin hedeflerinde var mi?
            Optional<Hedefler> iliskiliHedefOpt = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId()
                    , hedefNo, hedefTipi.getHedefKodu());
            if (iliskiliHedefOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
            }
            Hedefler iliskiliHedef = iliskiliHedefOpt.get();

            /*TODO: Eski sistemde  DetayMahkemeKararTalep ve HedeflerTalep birlike kaydediliyor ama
            aralarinda bir iliski kurulamiyor. HedeflerTalep kapatmaId'den gidilerek iliskili mahkeme karar bulunuyor
            fakat bu da sorunlara sebebiyet veriyor. bu DetayMahkemeKararTalep ile HedeflerTalep arasinda iliski kurmak icin
            HedeflerDetayTalep kaydediyoruz.
             **/
            DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
            DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

            HedeflerDetayTalep hedeflerDetayTalep = kararRequestMapper.toHedeflerDetayTalep(iDHedefDetay, mahkemeKararTalepId, iliskiliHedef.getId(), kullaniciId, kayitTarihi);
            hedeflerDetayTalep.setDetayMahkemeKararTalepId(savedDMahkemeKararTalep.getId());
            HedeflerDetayTalep savedHedeflerDetayTalep = dbHedeflerDetayTalepService.save(hedeflerDetayTalep);
            if(savedHedeflerDetayTalep == null){
                throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_HEDEFLER_DETAY_KAYDETMEHATASI, hedefNo);
            }

            HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(iDHedefDetay, mahkemeKararTalepId, kullaniciId, kayitTarihi);
            hedeflerTalepEntity.setKapatmaKararId(mahkemeKararOpt.get().getId());

            HedeflerTalep savedHedeflerTalep = dbHedeflerTalepService.save(hedeflerTalepEntity);
            //Hedef Aidiyat Talepleri Kaydet
            if (iDHedefDetay.getHedefAidiyatKodlari() != null) {
                saveHedeflerTalepAidiyatListesi(iDHedefDetay.getHedefAidiyatKodlari()
                        , savedHedeflerTalep.getId()
                        , savedHedeflerTalep.getHedefNo()
                        , kayitTarihi
                        , kullaniciId);
            }
        }

        return mahkemeKararTalepId;

    }

    //TODO : Sonlandirma karari icin hedef - aidiyat kodu gonderilmesi cok mantkli degil. Musteri ile konusulup netlestirilmeli
    private List<HedeflerAidiyatTalep> saveHedeflerTalepAidiyatListesi(List<String> sucKoduListesi, Long hedeflerTalepId, String hedefNo, LocalDateTime islemTarihi, Long kullaniciId) {
        List<HedeflerAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : sucKoduListesi) {

            HedeflerAidiyatTalep hedeflerAidiyatTalep = HedeflerAidiyatTalep.builder()
                    .hedefTalepId(hedeflerTalepId)
                    .aidiyatKod(aidiyatKodu)
                    .tarih(islemTarihi)
                    .kullaniciId(kullaniciId)
                    .build();

            HedeflerAidiyatTalep savedHedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.save(hedeflerAidiyatTalep);

            if (savedHedeflerAidiyatTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_HEDEFLER_AIDIYAT_KAYDETMEHATASI, hedefNo, aidiyatKodu);
            }

            result.add(savedHedeflerAidiyatTalep);
        }
        return result;
    }


}

