package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.model.entity.iym.talep.HtsHedeflerTalep;
import iym.common.service.db.mktalep.DbHtsHedeflerTalepService;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.ITHedefDetay;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import iym.makos.model.dto.mktalep.request.it.ITKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Component
@Slf4j
public class ITKararDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<ITKararRequest> {

    private KararRequestMapper kararRequestMapper;
    private DbHtsHedeflerTalepService dbHtsHedeflerTalepService;

    @Autowired
    public ITKararDBSaveHandler(DbHtsHedeflerTalepService dbHtsHedeflerTalepService, KararRequestMapper kararRequestMapper) {
        this.dbHtsHedeflerTalepService = dbHtsHedeflerTalepService;
        this.kararRequestMapper = kararRequestMapper;
    }
    @Override
    @Transactional
    public void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, ITKararRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {
        Long htsMahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();

        //buradaki mahkeme karar talep id  HTS_MAHKEME_KARAR_TALEP tablosunun id'sidir.
        for (ITHedefDetay ITHedefDetay : request.getHedefDetayListesi()) {
            HtsHedeflerTalep htsHedeflerTalep = new HtsHedeflerTalep();
            htsHedeflerTalep.setMahkemeKararId(htsMahkemeKararTalepId);
            HtsHedeflerTalep savedHtsHedeflerTalep = dbHtsHedeflerTalepService.save(htsHedeflerTalep);
            if(savedHtsHedeflerTalep == null){

            }
        }
    }
    //@Override
    public Long kaydet1(ITKararRequest request, LocalDateTime kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = null;//mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);

            //buradaki mahkeme karar talep id  HTS_MAHKEME_KARAR_TALEP tablosunun id'sidir.
            for (ITHedefDetay ITHedefDetay : request.getHedefDetayListesi()) {
                HtsHedeflerTalep htsHedeflerTalep = new HtsHedeflerTalep();
                htsHedeflerTalep.setMahkemeKararId(mahkemeKararTalepId);
                dbHtsHedeflerTalepService.save(htsHedeflerTalep);
            }

            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("ITKarar handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

