package iym.makos.domain.mktalep.requestprocessor.processor;

import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.request.id.IDSonlandirmaKarariRequest;
import iym.makos.model.dto.mktalep.request.id.IDSonlandirmaKarariResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@Slf4j
public class IDSonlandirmaKarariRequestProcessor extends MakosRequestProcessorBase<IDSonlandirmaKarariRequest, IDSonlandirmaKarariResponse> {

    @Override
    public IDSonlandirmaKarariResponse process(IDSonlandirmaKarariRequest request, MakosUserDetails islemYapanKullanici) {

        MakosApiResponse preCheck = preCheck(request, islemYapanKullanici);
        log.error("IDSonlandirmaKarari process failed: reason:{}", preCheck.getResponseMessage());
        if (preCheck.getResponseCode() != MakosResponseCode.SUCCESS){
            return IDSonlandirmaKarariResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage(preCheck.getResponseMessage())
                            .build())
                    .build();
        }

        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {

                return IDSonlandirmaKarariResponse.builder()
                        .requestId(request.getId())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            LocalDateTime kayitTarihi = LocalDateTime.now();
            Long kaydedenKullaniciId = islemYapanKullanici.getUserId();
            MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = requestSaver.kaydet(request, kayitTarihi, kaydedenKullaniciId);

            //TODO : handle null result
            if(mahkemeKararTalepIdWithEvrakId == null){

            }

            return IDSonlandirmaKarariResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepIdWithEvrakId.getEvrakId())
                    .mahkemeKararTalepId(mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId())
                    .build();

        } catch (MakosResponseException ex) {
            // Let MakosResponseException propagate with its detailed message
            log.error("IDSonlandirmaKarari process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDSonlandirmaKarariResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        } catch (Exception ex) {
            log.error("IDSonlandirmaKarari process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDSonlandirmaKarariResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        }

    }
}
