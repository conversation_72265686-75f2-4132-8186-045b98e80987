package iym.makos.domain.mktaleprequest.dbhandler;

import iym.common.enums.EvrakTuru;
import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mktalep.DbHtsMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararRequestDbSaveHandlerBase;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.api.MahkemeKararBilgisi;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.utils.UtilService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

/*
Test Senaryoları:
request == null	-> kaydet_ShouldThrowException_WhenRequestIsNull
kayitTarihi == null	-> kaydet_ShouldThrowException_WhenKayitTarihiIsNull
kullaniciId == null	-> kaydet_ShouldThrowException_WhenKullaniciIdIsNull
saveEvrakBilgileri → null	-> kaydet_ShouldThrowException_WhenSavedEvrakIsNull
saveEvrakBilgileri → id null -> kaydet_ShouldThrowException_WhenSavedEvrakIdIsNull
EvrakTuru = GENEL_EVRAK / ILETISIMIN_DENETLENMESI -> kaydet_ShouldSaveMahkemeKararTalep_WhenEvrakTuruGenelEvrek
EvrakTuru = ILETISIMIN_TESPITI	-> kaydet_ShouldSaveHtsMahkemeKararTalep_WhenEvrakTuruIletisiminTespiti
kaydet_ShouldThrowException_EvrakKayitFailure
kaydet_ShouldThrowException_HtsMahkemeKararTalepFailure
kaydet_ShouldThrowException_MahkemeKararTalepFailure
*
* */
@ExtendWith(MockitoExtension.class)
class MahkemeKararRequestDbSaveHandlerBaseTest extends BaseDomainUnitTest {

    @Mock
    private UtilService utilService;

    @Mock
    private KararRequestMapper kararRequestMapper;

    @Mock
    private DbEvrakKayitService dbEvrakKayitService;

    @Mock
    private DbMahkemeBilgiService dbMahkemeBilgiService;

    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;

    @Mock
    private DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;

    @InjectMocks
    private MahkemeKararRequestDbSaveHandlerBase    dbSaver = spy(new MahkemeKararRequestDbSaveHandlerBase<MkTalepRequest>() {
        @Override
        public void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId idWithEvrakId, MkTalepRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {
            // Dummy
        }
    });

    @Mock
    private MkTalepRequest testRequest;

    @Mock
    private  EvrakDetay evrakDetay;

    private LocalDateTime testDate;
    private Long testUserId;

    @BeforeEach
    void setUp() {
        testDate = createTestDate();
        testUserId = TEST_USER_ID;

    }

    @Test
    void kaydet_ShouldThrowException_WhenRequestIsNull() {
        assertThatThrownBy(() -> dbSaver.kaydet(null, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("request boş olamaz");
    }

    @Test
    void kaydet_ShouldThrowException_WhenKayitTarihiNull() {
        assertThatThrownBy(() -> dbSaver.kaydet(testRequest, null, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kayitTarihi boş olamaz");
    }

    @Test
    void kaydet_ShouldThrowException_WhenKullaniciIdNull() {
        assertThatThrownBy(() -> dbSaver.kaydet(testRequest, testDate, null))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kullaniciId boş olamaz");
    }

    @Test
    void kaydet_ShouldThrowException_WhenEvrakKurumKoduIsNull() {
        EvrakDetay detay = spy(createValidEvrakDetay());
        when(testRequest.getEvrakDetay()).thenReturn(detay);
        when(detay.getEvrakKurumKodu()).thenReturn(null);
        assertThatThrownBy(() -> dbSaver.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("evrakKurumKodu boş olamaz");

        verify(testRequest).getEvrakDetay();
        verify(testRequest, times(1)).getEvrakDetay();

    }


    @Test
    void kaydet_ShouldThrowException_WhenEvrakSiraNoNullOrEmpty() {

        when(utilService.getEvrakSiraNumarasi(anyString(), anyString())).thenReturn(null);

        when(testRequest.getEvrakDetay()).thenReturn(createValidEvrakDetay());
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());

        assertThatThrownBy(() -> dbSaver.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining(MakosResponseErrorCodes.EVRAK_SIRANO_ALINAMADI);

        verify(utilService).getEvrakSiraNumarasi(any(), any());
        verify(testRequest).getEvrakDetay();
        verify(testRequest).getMahkemeKararBilgisi();

    }

    @Test
    void kaydet_ShouldThrowException_WhenSavedEvrakIsNull() {

        when(testRequest.getEvrakDetay()).thenReturn(createValidEvrakDetay());
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());

        //happy-path testinde  getEvrakSiraNumarasi metodu boş olmayan bir değer döndürmesi gerekir.
        when(utilService.getEvrakSiraNumarasi(any(), any())).thenReturn("EV-123");
        when(kararRequestMapper.toEvrakKayit(any(), any(), any(), any(), any())).thenReturn(new EvrakKayit());
        when(dbEvrakKayitService.save(any())).thenReturn(null); // savedEvrak null

        // Act & Assert
        assertThatThrownBy(() -> dbSaver.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining(MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);

        verify(utilService).getEvrakSiraNumarasi(any(), any());
        verify(testRequest).getEvrakDetay();
        verify(testRequest).getMahkemeKararBilgisi();
        verify(kararRequestMapper).toEvrakKayit(any(), any(), any(), any(), any());
        verify(dbEvrakKayitService).save(any());

    }

    @Test
    void kaydet_ShouldThrowException_WhenSavedEvrakIdIsNull() {

        EvrakDetay evrakDetay = spy(createValidEvrakDetay());
        when(testRequest.getEvrakDetay()).thenReturn(evrakDetay);
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());

        EvrakKayit evrakWithoutId = new EvrakKayit();

        //happy-path testinde  getEvrakSiraNumarasi metodu boş olmayan bir değer döndürmesi gerekir.
        when(utilService.getEvrakSiraNumarasi(any(), any())).thenReturn("EV-123");

        //evrakDetay.getEvrakKurumKodu()boş olmayan bir değer döndürmesi gerekir
        when(evrakDetay.getEvrakKurumKodu()).thenReturn("TEST_KURUM");

        when(kararRequestMapper.toEvrakKayit(any(), any(), any(), any(), any())).thenReturn(evrakWithoutId);
        when(dbEvrakKayitService.save(any())).thenReturn(evrakWithoutId);

        // Act & Assert
        assertThatThrownBy(() -> dbSaver.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining(MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);

        verify(dbEvrakKayitService).save(any());
    }

    @Test
    void kaydet_ShouldThrowException_WhenEvrakKayitNull() {

        EvrakDetay evrakDetay = spy(createValidEvrakDetay());
        when(testRequest.getEvrakDetay()).thenReturn(evrakDetay);
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());

        //happy-path testinde  getEvrakSiraNumarasi metodu boş olmayan bir değer döndürmesi gerekir.
        when(utilService.getEvrakSiraNumarasi(any(), any())).thenReturn("EV-123");

        //evrakDetay.getEvrakKurumKodu()boş olmayan bir değer döndürmesi gerekir
        when(evrakDetay.getEvrakKurumKodu()).thenReturn("TEST_KURUM");

        when(kararRequestMapper.toEvrakKayit(any(), any(), any(), any(), any())).thenReturn(new EvrakKayit());
        when(dbEvrakKayitService.save(any())).thenReturn(null);

        // Act & Assert
        assertThatThrownBy(() -> dbSaver.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining(MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);

        verify(dbEvrakKayitService).save(any());

    }

    @Test
    void kaydet_ShouldSqvedEvrakKayitSuccessfully_whenAllOperationsSucceed() {

        EvrakDetay evrakDetay = spy(createValidEvrakDetay());
        when(testRequest.getEvrakDetay()).thenReturn(evrakDetay);
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());

        //happy-path testinde  getEvrakSiraNumarasi metodu boş olmayan bir değer döndürmesi gerekir.
        when(utilService.getEvrakSiraNumarasi(any(), any())).thenReturn("EV-123");

        //evrakDetay.getEvrakKurumKodu()boş olmayan bir değer döndürmesi gerekir
        when(evrakDetay.getEvrakKurumKodu()).thenReturn("TEST_KURUM");

        EvrakKayit expectedEvrakKayit =  EvrakKayit.builder()
                .id(100L)
                .build();

        EvrakKayit savedEvrakKayit =  EvrakKayit.builder()
                .id(100L)
                .build();

        when(kararRequestMapper.toEvrakKayit(any(), any(), any(), any(), any())).thenReturn(savedEvrakKayit);
  //      when(kararRequestMapper.toMahkemeKararTalep(any(), any(), any(), any())).thenReturn(new MahkemeKararTalep());

        when(dbEvrakKayitService.save(savedEvrakKayit)).thenReturn(expectedEvrakKayit);

        MahkemeKararTalep expectedTalep = MahkemeKararTalep.builder()
                .id(200L)
                .build();

        when(dbMahkemeKararTalepService.save(any())).thenReturn(expectedTalep);

        //WHEN
        MahkemeKararTalepIdWithEvrakId result = dbSaver.kaydet(testRequest, testDate, testUserId);

        //THEN
        assertThat(expectedEvrakKayit).isNotNull();
        assertThat(expectedEvrakKayit.getId()).isEqualTo(savedEvrakKayit.getId());


        verify(dbEvrakKayitService).save(any());


    }

    @Test
    void kaydet_ShouldSaveHtsTalep_WhenEvrakTuruIsILETISIMIN_TESPITI() {

        //GIVEN
        //talep = ILETISIMIN_TESPITI

        //evrakDetay.setEvrakTuru(EvrakTuru.ILETISIMIN_TESPITI);

        HtsMahkemeKararTalep requestedMahkemeKararTalep = new HtsMahkemeKararTalep();

        MahkemeKararTalepIdWithEvrakId expected = MahkemeKararTalepIdWithEvrakId.builder()
                .evrakId(12345L)
                .mahkemeKararTalepId(4321L)
                .build();

        Long expectedMahkemeKararTalepId = 4321L;
        HtsMahkemeKararTalep expectedMahkemeKararTalep = new HtsMahkemeKararTalep();
        expectedMahkemeKararTalep.setId(expectedMahkemeKararTalepId);

        String expectedEvrakSiraNo = "EVRAK-01";

        when(utilService.getEvrakSiraNumarasi(anyString(), anyString())).thenReturn(expectedEvrakSiraNo);
        EvrakKayit mockEvrakKayit = new EvrakKayit();
        mockEvrakKayit.setId(expected.getEvrakId());
        when(kararRequestMapper.toEvrakKayit(any(), any(), any(), any(), any())).thenReturn(mockEvrakKayit);
        when(dbEvrakKayitService.save(any())).thenReturn(mockEvrakKayit);
        when(kararRequestMapper.toHTSMahkemeKararTalep(any(), any(), any(), any())).thenReturn(requestedMahkemeKararTalep);
        when(dbHtsMahkemeKararTalepService.save(requestedMahkemeKararTalep)).thenReturn(expectedMahkemeKararTalep);

        // Set up evrak türü for the test
        EvrakDetay mockEvrakDetay = createValidEvrakDetay();
        mockEvrakDetay.setEvrakTuru(EvrakTuru.ILETISIMIN_TESPITI);
        when(testRequest.getEvrakDetay()).thenReturn(mockEvrakDetay);
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());
        when(testRequest.getKararTuru()).thenReturn(KararTuru.ILETISIMIN_TESPITI);

        //WHEN
        MahkemeKararTalepIdWithEvrakId result = dbSaver.kaydet(testRequest, testDate, testUserId);

        //THEN
        assertThat(result).isNotNull();
        assertThat(result.getEvrakId()).isEqualTo(expected.getEvrakId());
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(expected.getMahkemeKararTalepId());

        //VERIFY
        verify(utilService).getEvrakSiraNumarasi(anyString(), anyString());
        verify(kararRequestMapper).toEvrakKayit(any(), any(), any(), any(), any());
        verify(dbEvrakKayitService).save(any(EvrakKayit.class));
        verify(kararRequestMapper).toHTSMahkemeKararTalep(any(), any(), any(), any());
        verify(dbHtsMahkemeKararTalepService).save(requestedMahkemeKararTalep);

        //sadece iletisimini tespitinin cagrildigini verify et.
        verify(kararRequestMapper, never()).toMahkemeKararTalep(any(), anyLong(), anyLong(), any());
        verify(dbMahkemeKararTalepService, never()).save(any());

    }


    @ParameterizedTest
    @EnumSource(value = EvrakTuru.class, names = {"ILETISIMIN_DENETLENMESI", "GENEL_EVRAK"})
    void kaydet_ShouldSaveMahkemeTalep_WhenEvrakTuruIsILETISIMIN_DENETLENMESI_YENI_KARAR(EvrakTuru evrakTuru) {
        //GIVEN
        //evrakDetay.setEvrakTuru(evrakTuru);

        MahkemeKararTalepIdWithEvrakId expected = MahkemeKararTalepIdWithEvrakId.builder()
                .evrakId(12345L)
                .mahkemeKararTalepId(4321L)
                .build();

        MahkemeKararTalep requestedMahkemeKararTalep = new MahkemeKararTalep();

        Long expectedMahkemeKararTalepId = 4321L;
        MahkemeKararTalep expectedMahkemeKararTalep = new MahkemeKararTalep();
        expectedMahkemeKararTalep.setId(expectedMahkemeKararTalepId);

        String expectedEvrakSiraNo = "EVRAK-01";

        when(utilService.getEvrakSiraNumarasi(anyString(), anyString())).thenReturn(expectedEvrakSiraNo);
        EvrakKayit mockEvrakKayit = new EvrakKayit();
        mockEvrakKayit.setId(12345L);
        when(kararRequestMapper.toEvrakKayit(any(), any(), any(), any(), any())).thenReturn(mockEvrakKayit);
        when(dbEvrakKayitService.save(any())).thenReturn(mockEvrakKayit);
        when(kararRequestMapper.toMahkemeKararTalep(any(), any(), any(), any())).thenReturn(requestedMahkemeKararTalep);
        when(dbMahkemeKararTalepService.save(requestedMahkemeKararTalep)).thenReturn(expectedMahkemeKararTalep);

        // Set up evrak türü for the test
        EvrakDetay mockEvrakDetay = createValidEvrakDetay();
        mockEvrakDetay.setEvrakTuru(evrakTuru);
        when(testRequest.getEvrakDetay()).thenReturn(mockEvrakDetay);
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());
        when(testRequest.getKararTuru()).thenReturn(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR);

        //WHEN
        MahkemeKararTalepIdWithEvrakId result = dbSaver.kaydet(testRequest, testDate, testUserId);


        //THEN
        assertThat(result).isNotNull();
        assertThat(result.getEvrakId()).isEqualTo(expected.getEvrakId());
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(expected.getMahkemeKararTalepId());


        //VERIFY
        verify(utilService).getEvrakSiraNumarasi(anyString(), anyString());
        verify(kararRequestMapper).toEvrakKayit(any(), any(), any(), any(), any());
        verify(dbEvrakKayitService).save(any());
        verify(kararRequestMapper).toMahkemeKararTalep(any(), any(), any(), any());
        verify(dbMahkemeKararTalepService).save(requestedMahkemeKararTalep);

        //iletisimin tespiti'nin cagrilmadigini verify et
        verify(kararRequestMapper, never()).toHTSMahkemeKararTalep(any(), any(), any(), any());
        verify(dbHtsMahkemeKararTalepService, never()).save(any());

        /*TODO:
         dbMahkemeKararTalepService.save(...) gerçekten çağrılmış mı ve doğru nesneyle çağrılmış mı?
        ArgumentCaptor<MahkemeKararTalep> captor = ArgumentCaptor.forClass(MahkemeKararTalep.class);
        verify(dbMahkemeKararTalepService).save(captor.capture());
        assertThat(captor.getValue()).isSameAs(requestedMahkemeKararTalep);
        * */
    }



    private EvrakDetay createValidEvrakDetay() {
        return EvrakDetay.builder()
                .evrakNo("TEST-EVRAK-001")
                .evrakTarihi(LocalDateTime.now())
                .evrakKurumKodu("TEST-KURUM")
                .evrakTuru(iym.common.enums.EvrakTuru.ILETISIMIN_DENETLENMESI)
                .geldigiIlIlceKodu("0601")
                .acilmi(false)
                .build();
    }

    private MahkemeKararBilgisi createValidMahkemeKararBilgisi() {
        return MahkemeKararBilgisi.builder()
                .mahkemeKararTipi(iym.common.enums.MahkemeKararTip.ADLI_HAKIM_KARARI)
                .mahkemeKararDetay(createValidMahkemeKararDetay())
                .build();
    }

    private iym.makos.model.api.MahkemeKararDetay createValidMahkemeKararDetay() {
        return iym.makos.model.api.MahkemeKararDetay.builder()
                .mahkemeKodu("TEST-MAHKEME")
                .mahkemeKararNo("TEST-KARAR-001")
                .mahkemeIlIlceKodu("0601")
                .sorusturmaNo("TEST-SORUSTURMA")
                .build();
    }


}
