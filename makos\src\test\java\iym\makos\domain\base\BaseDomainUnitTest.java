package iym.makos.domain.base;

import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;

/**
 * Base class for all MAKOS domain unit tests.
 * <p>
 * This class provides:
 * - Mockito extension for mocking dependencies
 * - Test profile activation for proper configuration
 * - Common test utilities and patterns
 * <p>
 * Usage:
 * - Extend this class for all domain unit tests
 * - Use @Mock for dependencies
 * - Use @InjectMocks for the class under test
 * - Follow Given-When-Then pattern in test methods
 * 
 * <AUTHOR> Team
 */
@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public abstract class BaseDomainUnitTest {
    
    /**
     * Common test constants
     */
    protected static final Long TEST_USER_ID = 12345L;
    protected static final String TEST_EVRAK_NO = "2024/TEST/001";
    protected static final String TEST_KURUM_KODU = "BTK";
    protected static final String TEST_IL_ILCE_KODU = "0600";
    
    /**
     * Helper method to create test timestamps
     */
    protected LocalDateTime createTestDate() {
        return LocalDateTime.now();
    }
    
    /**
     * Helper method to create test date with specific offset
     */
    protected java.util.Date createTestDate(int daysOffset) {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.add(java.util.Calendar.DAY_OF_MONTH, daysOffset);
        return cal.getTime();
    }

    protected MahkemeKararTalepIdWithEvrakId createTestMahkemeKararTalepIdWithEvrakId(){
        return  MahkemeKararTalepIdWithEvrakId.builder()
                .mahkemeKararTalepId(100L)
                .evrakId(200L)
                .build();
    }

}
