package iym.common.service.db;

import iym.common.model.entity.iym.SucTipi;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeBilgi entity
 */
public interface DbSucTipiService extends GenericDbService<SucTipi, String> {

    Optional<SucTipi> findBySucTipiKodu(String sucTipiKodu);

    List<SucTipi> findByDurum(String durum);

    List<SucTipi> findByMahkemeKararTipi(String mahkemeKaraTipiKodu);


    List<SucTipi> getByMahkemeTalepId(Long mahkemeKararTalepId);

    List<SucTipi> getByMahkemeIslemId(Long mahkemeKararIslemId);

    List<SucTipi> getByMahkemeId(Long mahkemeKararId);

}
