package iym.makos.service;

import iym.common.model.entity.iym.SucTipi;
import iym.common.service.db.DbSucTipiService;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.SucTipiMapper;
import iym.makos.model.dto.db.SucTipiDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SucTipiService {

    private final DbSucTipiService dbSucTipiService;
    private final SucTipiMapper sucTipiMapper;

    public List<SucTipiDTO> findByMahkemeKararTipi(String mahkemeKaraTipiKodu) {
        List<SucTipiDTO> result = null;
        try {
            List<SucTipi> sucTipiList = dbSucTipiService.findByMahkemeKararTipi(mahkemeKaraTipiKodu);
            if(sucTipiList == null){
                throw new MakosResponseException(String.format("Mahkeme Karar Tipi % için suç tipi bulunamadı", mahkemeKaraTipiKodu));
            }

            result = sucTipiMapper.toDtoList(sucTipiList);

        } catch (DataAccessException dae) {
            throw new MakosResponseException("Veritabanı hatası: Suç tipleri alınamadı", dae);
        } catch (Exception ex) {
            throw new MakosResponseException("Beklenmeyen hata oluştu", ex);
        }
        if(result == null){
            result = new ArrayList<>();
        }

        return result;
    }

    public Optional<SucTipiDTO> getSucTipiByKodu(String sucTipiKodu) {
        return dbSucTipiService.findBySucTipiKodu(sucTipiKodu)
                .map(sucTipiMapper::toDto);
    }

    public List<SucTipiDTO> findByDurum(String durum) {
        List<SucTipi> sucTipleri = dbSucTipiService.findByDurum(durum);
        return sucTipiMapper.toDtoList(sucTipleri);
    }





}
