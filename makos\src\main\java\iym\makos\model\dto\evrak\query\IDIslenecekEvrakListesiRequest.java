package iym.makos.model.dto.evrak.query;

import iym.common.enums.KararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.model.MakosQueryRequest;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDIslenecekEvrakListesiRequest extends MakosQueryRequest {

    //null ise tum turleri getir
    KararTuru kararTuru;

    Long atananKullaniciId;

    String gorevTipi;

    boolean tanimlama;

    boolean onaylama;

    boolean nobetci;

    @Override
    public ValidationResult isValid() {
        ValidationResult validationResult = new ValidationResult(true);
        if (kararTuru != null && !kararTuru.isIDKararTuru()){
            validationResult.addFailedReason("Karar türü: 'Iletisimin Denetlenmesi' tipinde olmalıdır");
            return validationResult;
        }

        return validationResult;
    }
}

