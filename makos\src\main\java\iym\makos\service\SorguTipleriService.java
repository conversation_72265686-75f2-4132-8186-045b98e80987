package iym.makos.service;

import iym.common.enums.ResultCode;
import iym.common.model.api.Response;
import iym.common.model.entity.iym.SorguTipleri;
import iym.common.service.db.DbSorguTipleriService;
import iym.common.util.CommonUtils;
import iym.makos.mapper.SorguTipiMapper;
import iym.makos.model.dto.db.SorguTipiDTO;
import iym.makos.model.dto.view.IDMahkemeKarariInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SorguTipleriService {

    private final DbSorguTipleriService dbSorguTipleriService;
    private final SorguTipiMapper sorguTipiMapper;

    public List<SorguTipiDTO> findAllSorguTipleri(){
        List<SorguTipleri> sorguTipleri = dbSorguTipleriService.findAll();

        List<SorguTipiDTO> result = CommonUtils.safeList(sorguTipleri
                ).stream()
                .map(sorguTipiMapper::toDto)
                .collect(Collectors.toList());

        return result;

    }

    public Response<List<SorguTipiDTO>> findAllSorguTipleri2() {

        Response<List<SorguTipiDTO>> result = null;
        log.debug("findAllSorguTipleri");

        try {

            List<SorguTipleri> sorguTipleri = dbSorguTipleriService.findAll();

            List<SorguTipiDTO> list = CommonUtils.safeList(sorguTipleri
                    ).stream()
                    .map(sorguTipiMapper::toDto)
                    .collect(Collectors.toList());

            result = new Response<>(list);

        } catch (Exception ex) {
            return new Response<>(ResultCode.FAILED, "findAllSorguTipleri hatası", ex);
        }

        return result;
    }


}
