package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.EvrakKurum;
import iym.common.enums.EvrakTuru;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mktalep.DbHtsMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.utils.UtilService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@Slf4j
public class MahkemeKararRequestCommonDbSaver {

    private final UtilService utilService;
    private final KararRequestMapper kararRequestMapper;
    private final DbEvrakKayitService dbEvrakKayitService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;

    @Autowired
    public MahkemeKararRequestCommonDbSaver(UtilService utilService
            , KararRequestMapper kararRequestMapper
            , DbEvrakKayitService dbEvrakKayitService
            , DbMahkemeBilgiService dbMahkemeBilgiService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService
            , DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService) {
        this.utilService = utilService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbEvrakKayitService = dbEvrakKayitService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.dbHtsMahkemeKararTalepService = dbHtsMahkemeKararTalepService;

    }

    public MahkemeKararTalepIdWithEvrakId handleDbSave(MkTalepRequest kararRequest, LocalDateTime kayitTarihi, Long kullaniciId) {
        // Parametre kontrolleri
        if (kararRequest == null) {
            throw new MakosResponseException(MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "kararRequest cannot be null");
        }
        if (kayitTarihi == null) {
            throw new MakosResponseException(MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "kayitTarihi cannot be null");
        }
        if (kullaniciId == null) {
            throw new MakosResponseException(MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "kullaniciId cannot be null");
        }

        try {
            EvrakDetay evrakDetay = kararRequest.getEvrakDetay();
            String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            MahkemeKararTip kararTipi = kararRequest.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

            // Evrak kaydet
            EvrakKayit savedEvrak = saveEvrakBilgileri(
                    evrakDetay,
                    evrakTipi,
                    kayitTarihi,
                    kullaniciId,
                    kararRequest.getKararTuru()
            );
            if (savedEvrak == null || savedEvrak.getId() == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);
            }
            Long savedEvrakId = savedEvrak.getId();

            Long mahkemeKararTalepId = null;

            // Evrak türüne göre talep oluştur
            EvrakTuru evrakTuru = kararRequest.getEvrakDetay().getEvrakTuru();
            if (evrakTuru == EvrakTuru.ILETISIMIN_TESPITI) {
                HtsMahkemeKararTalep talep = kararRequestMapper.toHTSMahkemeKararTalep(
                        kararRequest.getMahkemeKararBilgisi(),
                        savedEvrakId,
                        kullaniciId,
                        kayitTarihi
                );
                HtsMahkemeKararTalep saved = dbHtsMahkemeKararTalepService.save(talep);
                if (saved == null || saved.getId() == null) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_KAYIT_HATASI);
                }
                mahkemeKararTalepId = saved.getId();

            } else if (evrakTuru == EvrakTuru.ILETISIMIN_DENETLENMESI || evrakTuru == EvrakTuru.GENEL_EVRAK) {
                MahkemeKararTalep talep = kararRequestMapper.toMahkemeKararTalep(
                        kararRequest.getMahkemeKararBilgisi(),
                        savedEvrakId,
                        kullaniciId,
                        kayitTarihi
                );
                MahkemeKararTalep saved = dbMahkemeKararTalepService.save(talep);
                if (saved == null || saved.getId() == null) {
                    throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_KAYIT_HATASI);
                }
                mahkemeKararTalepId = saved.getId();

            }

            MahkemeKararTalepIdWithEvrakId sonuc = MahkemeKararTalepIdWithEvrakId.builder()
                    .mahkemeKararTalepId(mahkemeKararTalepId)
                    .evrakId(savedEvrakId)
                    .build();

            return sonuc;
        } catch (DataAccessException ex1) {
            // DB spesifik hata
            throw new MakosResponseException(MakosResponseErrorCodes.VERITABANI_HATASI, ex1.getMessage(), ex1);
        } catch (MakosResponseException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new MakosResponseException(MakosResponseErrorCodes.BILINMEYEN_HATA, ex.getMessage(), ex);
        }
    }

    private EvrakKayit saveEvrakBilgileri(EvrakDetay evrakDetay, String evrakTipi, LocalDateTime islemTarihi, Long kaydedenKullaniciId, KararTuru kararTuru) {

        String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
        EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

        //Evrak sira numarasi
        String evrakSiraNo = utilService.getEvrakSiraNumarasi(evrakDetay.getEvrakKurumKodu(), evrakDetay.getEvrakTuru().name());
        if (CommonUtils.isNullOrEmpty(evrakSiraNo)) {
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_SIRANO_ALINAMADI);
        }
        //Save Evrak
        EvrakKayit evrakKayit = kararRequestMapper.toEvrakKayit(evrakDetay, evrakSiraNo, evrakTipi, islemTarihi, kaydedenKullaniciId);

        return dbEvrakKayitService.save(evrakKayit);
    }
}
