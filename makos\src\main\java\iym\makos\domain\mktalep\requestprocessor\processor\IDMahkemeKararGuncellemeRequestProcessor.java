package iym.makos.domain.mktalep.requestprocessor.processor;

import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.request.id.IDMahkemeKararGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.IDMahkemeKararGuncellemeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@Slf4j
public class IDMahkemeKararGuncellemeRequestProcessor extends MakosRequestProcessorBase<IDMahkemeKararGuncellemeRequest, IDMahkemeKararGuncellemeResponse> {

    @Override
    public IDMahkemeKararGuncellemeResponse process(IDMahkemeKararGuncellemeRequest request, MakosUserDetails islemYapanKullanici) {

        MakosApiResponse preCheck = preCheck(request, islemYapanKullanici);
        log.error("IDMahkemeKararGuncelleme process failed: reason:{}", preCheck.getResponseMessage());
        if (preCheck.getResponseCode() != MakosResponseCode.SUCCESS){
            return IDMahkemeKararGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage(preCheck.getResponseMessage())
                            .build())
                    .build();
        }

        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {

                return IDMahkemeKararGuncellemeResponse.builder()
                        .requestId(request.getId())
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            LocalDateTime kayitTarihi = LocalDateTime.now();
            Long kaydedenKullaniciId = islemYapanKullanici.getUserId();
            MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = requestSaver.kaydet(request, kayitTarihi, kaydedenKullaniciId);
            //TODO : handle null result
            if(mahkemeKararTalepIdWithEvrakId == null){

            }

            return IDMahkemeKararGuncellemeResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepIdWithEvrakId.getEvrakId())
                    .mahkemeKararTalepId(mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId())
                    .build();

        } catch (MakosResponseException ex) {
            log.error("IDMahkemeKararGuncelleme process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDMahkemeKararGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        } catch (Exception ex) {
            log.error("IDMahkemeKararGuncelleme process failed. id:{}, evrakNo:{}", getRequestId(request), getEvrakNo(request), ex);
            return IDMahkemeKararGuncellemeResponse.builder()
                    .requestId(getRequestId(request))
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        }
    }
}
