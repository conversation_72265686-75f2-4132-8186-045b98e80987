package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.EvrakKurum;
import iym.common.enums.EvrakTuru;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mktalep.DbHtsMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.utils.UtilService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.GenericTypeResolver;
import org.springframework.dao.DataAccessException;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
public abstract class MahkemeKararRequestDbSaveHandlerBase<T extends MkTalepRequest> implements MahkemeKararDBSaveHandler<T> {

    protected UtilService utilService;
    protected KararRequestMapper kararRequestMapper;
    protected DbEvrakKayitService dbEvrakKayitService;
    protected DbMahkemeBilgiService dbMahkemeBilgiService;
    protected DbMahkemeKararTalepService dbMahkemeKararTalepService;
    protected DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;

    @Autowired
    public final void setMahkemeKararRequestCommonDbSaver(UtilService utilService
            , KararRequestMapper kararRequestMapper
            , DbEvrakKayitService dbEvrakKayitService
            , DbMahkemeBilgiService dbMahkemeBilgiService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService
            , DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService) {
        this.utilService = utilService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbEvrakKayitService = dbEvrakKayitService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.dbHtsMahkemeKararTalepService = dbHtsMahkemeKararTalepService;
    }


    @Override
    @Transactional
    public MahkemeKararTalepIdWithEvrakId kaydet(T request, LocalDateTime kayitTarihi, Long kullaniciId) {
        //MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
        MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId = requestKaydet(request, kayitTarihi, kullaniciId);

        //Ortak kaydetme işleminde bir hata var ise
        if (mahkemeKararTalepIdWithEvrakId == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getId(), MakosResponseErrorCodes.MKTALEP_KAYIT_HATASI + ". İşleme devam edilemiyor.");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        } else {
            advanceHandleDbSave(mahkemeKararTalepIdWithEvrakId, request, kayitTarihi, kullaniciId);
        }
        return mahkemeKararTalepIdWithEvrakId;
    }

    public abstract void advanceHandleDbSave(final MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, final T request, final LocalDateTime kayitTarihi, final Long kullaniciId);


    @SuppressWarnings("unchecked")
    public Class<T> getRelatedRequestType() {
        return (Class<T>) GenericTypeResolver.resolveTypeArgument(
                this.getClass(),
                MahkemeKararDBSaveHandler.class
        );
    }


    private MahkemeKararTalepIdWithEvrakId requestKaydet(MkTalepRequest kararRequest, LocalDateTime kayitTarihi, Long kullaniciId) {
        // Parametre kontrolleri
        validateRequest(kararRequest, kayitTarihi, kullaniciId);

        EvrakDetay evrakDetay = kararRequest.getEvrakDetay();
        String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
        if (CommonUtils.isNullOrEmpty(evrakGelenKurumKodu)) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "evrakKurumKodu boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
        EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

        MahkemeKararTip kararTipi = kararRequest.getMahkemeKararBilgisi().getMahkemeKararTipi();
        String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

        // Evrak kaydet
        EvrakKayit savedEvrak = saveEvrakBilgileri(evrakDetay, evrakTipi, kayitTarihi,kullaniciId, kararRequest.getKararTuru());
        if (savedEvrak == null || savedEvrak.getId() == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
        Long savedEvrakId = savedEvrak.getId();

        Long mahkemeKararTalepId = null;

        // Evrak türüne göre talep oluştur
        EvrakTuru evrakTuru = kararRequest.getEvrakDetay().getEvrakTuru();
        if (evrakTuru == EvrakTuru.ILETISIMIN_TESPITI) {
            HtsMahkemeKararTalep talep = kararRequestMapper.toHTSMahkemeKararTalep(
                    kararRequest.getMahkemeKararBilgisi(),
                    savedEvrakId,
                    kullaniciId,
                    kayitTarihi
            );
            HtsMahkemeKararTalep saved = dbHtsMahkemeKararTalepService.save(talep);
            if (saved == null || saved.getId() == null) {

                String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.HTSMKTALEP_KAYIT_HATASI);
                log.error(errorStr);
                throw new MakosResponseException(errorStr);
            }
            mahkemeKararTalepId = saved.getId();

        } else if (evrakTuru == EvrakTuru.ILETISIMIN_DENETLENMESI || evrakTuru == EvrakTuru.GENEL_EVRAK) {
            MahkemeKararTalep talep = kararRequestMapper.toMahkemeKararTalep(
                    kararRequest.getMahkemeKararBilgisi(),
                    savedEvrakId,
                    kullaniciId,
                    kayitTarihi
            );
            MahkemeKararTalep saved = dbMahkemeKararTalepService.save(talep);
            if (saved == null || saved.getId() == null) {

                String errorStr = CommonUtils.getFormattedStringWithUUID(kararRequest.getId(), MakosResponseErrorCodes.MKTALEP_KAYIT_HATASI);
                log.error(errorStr);
                throw new MakosResponseException(errorStr);
            }
            mahkemeKararTalepId = saved.getId();

        }


        MahkemeKararTalepIdWithEvrakId sonuc = MahkemeKararTalepIdWithEvrakId.builder()
                .mahkemeKararTalepId(mahkemeKararTalepId)
                .evrakId(savedEvrakId)
                .build();

        return sonuc;

    }

    private void  validateRequest(MkTalepRequest request, LocalDateTime kayitTarihi, Long kullaniciId) throws DataAccessException {
        if (request == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(null, MakosResponseErrorCodes.GECERSIZ_PARAMETRE , "request boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        if (kayitTarihi == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE , "kayitTarihi boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        if (kullaniciId == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE , "kullaniciId boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
    }

    private EvrakKayit saveEvrakBilgileri(EvrakDetay evrakDetay, String evrakTipi, LocalDateTime islemTarihi, Long kaydedenKullaniciId, KararTuru kararTuru) {

        String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
        if (CommonUtils.isNullOrEmpty(evrakGelenKurumKodu)) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(null, MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "evrakKurumKodu boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
        //Evrak sira numarasi
        String evrakSiraNo = utilService.getEvrakSiraNumarasi(evrakDetay.getEvrakKurumKodu(), evrakDetay.getEvrakTuru().name());
        if (CommonUtils.isNullOrEmpty(evrakSiraNo)) {
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_SIRANO_ALINAMADI);
        }
        //Save Evrak
        EvrakKayit evrakKayit = kararRequestMapper.toEvrakKayit(evrakDetay, evrakSiraNo, evrakTipi, islemTarihi, kaydedenKullaniciId);

        return dbEvrakKayitService.save(evrakKayit);
    }

}

