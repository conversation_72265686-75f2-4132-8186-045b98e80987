package iym.makos.controller;

import iym.common.enums.KararTuru;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.evrak.query.*;
import iym.makos.model.dto.mk.query.id.IDMahkemeKararSorgulamaRequest;
import iym.makos.model.dto.mk.query.id.IDMahkemeKararSorgulamaResponse;
import iym.makos.model.dto.mk.view.MahkemeKararSorguView;
import iym.makos.model.dto.mktalep.request.id.IDMahkemeKararAtamaRequest;
import iym.makos.model.dto.mktalep.request.id.IDMahkemeKararOnaylamaResponse;
import iym.makos.model.dto.evrak.view.IDEvrakAtamaDTO;
import iym.makos.model.dto.evrak.view.IDIslenecekEvrakDTO;
import iym.makos.service.mk.MahkemeKararService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.InvalidParameterException;
import java.util.List;


@RestController
@RequestMapping("iletisiminDenetlenmesiBtk")
@Slf4j
@Validated
public class IletisiminDenetlenmesiBTKController {

    @Autowired
    private MahkemeKararService mahkemeKararService;

    /*
    * ++ islenecekIDEvrakListesi
    * --idEvrakDeyatBilgileri Getir inner data validasyondaki gibi factory based olacak
    * ++ idEvrakAtama
    * --idEvrakMahkemeBilgiDetay - Mahkeme Bilgileri, Suc Bilgileri, Aidiyat Bilgileri vs ? Yada guncelleme ise_
    * --idEvrakIade ? idEvrakItiraz ?
    * ++ idHedefKismiIade
    * -- idEvrakOnaylama -> Artik mahkeme Karar Bilgisi Tanimlanir
    * ++ idEvrakAtamaHistory
    * */

    @PostMapping("/islenecekIDEvrakListele")
    public ResponseEntity<IDIslenecekEvrakListesiResponse> islenecekIDEvrakListele (
            @Valid @NotNull @RequestBody IDIslenecekEvrakListesiRequest sorguParam, Authentication authentication) {
        try {

            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("islenecekIDEvrakListesi Request geldi. ReqId{}", sorguParam.getId());

            List<IDIslenecekEvrakDTO> evrakListesi = mahkemeKararService.getBTKIslenecekEvrakListesi(MakosUserDetails.fromUserDetails(user), sorguParam.getGorevTipi(), sorguParam.isNobetci());

            return ResponseEntity.ok(IDIslenecekEvrakListesiResponse.builder()
                    .islenecekEvrakListesi(evrakListesi)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("islenecekIDEvrakListesi process failed, ReqId:{}", sorguParam.getId(), ex);
            IDIslenecekEvrakListesiResponse response = IDIslenecekEvrakListesiResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("İşlenecek Evrak Liste Sorgusu Başarısız.")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }

    @PostMapping("/idEvrakAtama")
    public ResponseEntity<IDEvrakAtamaResponse> idEvrakAtama(
            @Valid @RequestBody IDEvrakAtamaRequest request, Authentication authentication) {
        log.debug("mahkemeKararAtama request received. id:{}", request.getId());
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            log.info("mahkemeKararAtama Request received:{}, user:{}", request, user.getUsername());

            return ResponseEntity.ok(IDEvrakAtamaResponse.builder()
                    .evrakId(request.getEvrakId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage("Başarılı")
                            .build())
                    .build());

        }catch (Exception ex){
            log.error("mahkemeKararAtama  failed, requestId:{}, evrakId:{}", request.getId(), request.getEvrakId(), ex);
            IDEvrakAtamaResponse response = IDEvrakAtamaResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/idEvrakAtamaKaldir")
    public ResponseEntity<IDEvrakAtamaResponse> idEvrakAtamaKaldir(
            @Valid @RequestBody IDEvrakAtamaRequest request, Authentication authentication) {
        log.debug("mahkemeKararAtama request received. id:{}", request.getId());
        return null;
        /*
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            log.info("mahkemeKararAtama Request received:{}, user:{}", request, user.getUsername());

            return ResponseEntity.ok(IDEvrakAtamaResponse.builder()
                    .evrakId(request.getEvrakId())
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Başarılı")
                            .build())
                    .build());

        }catch (Exception ex){
            log.error("mahkemeKararAtama  failed, requestId:{}, evrakId:{}", request.getId(), request.getEvrakId(), ex);
            IDEvrakAtamaResponse response = IDEvrakAtamaResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }*/
    }

    @PostMapping("/idEvrakOnaylama")  //@PostMapping("/idEvrakTanimlama ayri olacak")
    public ResponseEntity<IDMahkemeKararOnaylamaResponse> idEvrakOnaylama(
            @Valid @RequestBody IDMahkemeKararAtamaRequest request, Authentication authentication) {
        log.debug("mahkemeKararOnaylama request received. id:{}", request.getId());

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            log.info("mahkemeKararOnaylama Request received:{}, user:{}", request, user.getUsername());

            IDMahkemeKararOnaylamaResponse response = null;//talepIslemService.process(request, user.getId());
            String aciklama = "";

            return ResponseEntity.ok(IDMahkemeKararOnaylamaResponse.builder()
                    .aciklama(aciklama)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage("Onaylama Başarılı")
                            .build())
                    .build());


        }catch (Exception ex){
            log.error("kararOnaylama request failed, requestId:{}, evrakId:{}", request.getId(), request.getEvrakId(), ex);
            IDMahkemeKararOnaylamaResponse response = IDMahkemeKararOnaylamaResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/idEvrakTanimlama")
    public ResponseEntity<IDMahkemeKararOnaylamaResponse> idEvrakTanimlama(
            @Valid @RequestBody IDMahkemeKararAtamaRequest request, Authentication authentication) {

        IDMahkemeKararOnaylamaResponse response = IDMahkemeKararOnaylamaResponse.builder()
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.FAILED)
                        .responseMessage("Internal Error")
                        .build())
                .build();
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @PostMapping("/idEvrakAtamaHistory")
    public ResponseEntity<IDEvrakAtamaHistoryResponse> idEvrakAtamaHistory(
            @Valid @RequestBody IDEvrakAtamaHistoryRequest sorguParam, Authentication authentication) {
        //sorguParam'dan sadece uuid geliyor. loglama amacli
        try {

            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("islenecekKararListesi Request geldi. ReqId{}", sorguParam.getId());

            List<IDEvrakAtamaDTO> evrakListesi = mahkemeKararService.getIDEvrakAtamaHistory(MakosUserDetails.fromUserDetails(user), sorguParam.getEvrakId());

            return ResponseEntity.ok(IDEvrakAtamaHistoryResponse.builder()
                    .atamaListesi(evrakListesi)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("islenecekKararListesi process failed, ReqId:{}", sorguParam.getId(), ex);
            IDEvrakAtamaHistoryResponse response = IDEvrakAtamaHistoryResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("İşlenecek Karar Liste Sorgusu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }


    @PostMapping("/idMahkemeKararSorgu")
    public ResponseEntity<IDMahkemeKararSorgulamaResponse> idMahkemeKararSorgu(
            @Valid @RequestBody IDMahkemeKararSorgulamaRequest sorguParam, Authentication authentication) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            List<MahkemeKararSorguView> sonucListesi = mahkemeKararService.mahkemeKararSorgu(MakosUserDetails.fromUserDetails(user), sorguParam.getSorguParam());

            return ResponseEntity.ok(IDMahkemeKararSorgulamaResponse.builder()
                    .kararlar(sonucListesi)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("mahkemeKararSorgu process failed, requestId:{}", sorguParam.hashCode(), ex);
            IDMahkemeKararSorgulamaResponse response = IDMahkemeKararSorgulamaResponse.builder()
                    .kararlar(null)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Sorgu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }


}
