package iym.makos.model.dto.mktalep.query;

import iym.makos.model.MakosRequestResponse;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepIslenecekView;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class MahkemeKararTalepIslenecekResponse extends MakosRequestResponse {
    private List<MahkemeKararTalepIslenecekView> islenecekKararlar;
} 