package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.GuncellemeTip;
import iym.common.model.entity.iym.mk.MahkemeAidiyat;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.mk.DbMahkemeKararAidiyatService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeAidiyatDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.GenelEvrakRequest;
import iym.makos.model.dto.mktalep.request.id.IDAidiyatBilgisiGuncellemeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDAidiyatBilgisiGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDAidiyatBilgisiGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final KararRequestMapper kararRequestMapper;
    private final DbMahkemeAidiyatDetayTalepService dbMahkemeAidiyatDetayTalepService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    private final DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService;

    @Autowired
    public IDAidiyatBilgisiGuncellemeDBSaveHandler(DbMahkemeKararService dbMahkemeKararService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService
            , KararRequestMapper kararRequestMapper
            , DbMahkemeAidiyatDetayTalepService dbMahkemeAidiyatDetayTalepService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService
            , DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbMahkemeAidiyatDetayTalepService = dbMahkemeAidiyatDetayTalepService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
        this.dbMahkemeKararAidiyatService = dbMahkemeKararAidiyatService;
    }

    @Override
    @Transactional
    public void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, IDAidiyatBilgisiGuncellemeRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {
        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();

        List<AidiyatGuncellemeKararDetay> guncellemeListesi = request.getAidiyatGuncellemeKararDetayListesi();
        if (guncellemeListesi != null) {
            for (AidiyatGuncellemeKararDetay guncellemeBilgisi : guncellemeListesi) {
                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay iliskiliMahkemeKararDetayRequest = guncellemeBilgisi.getMahkemeKararDetay();
                Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                        , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                if (iliskiliMahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetayRequest.getMahkemeKodu(), iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                }
                MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();


                DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

                List<AidiyatGuncellemeDetay> aidiyatGuncellemeListesi = guncellemeBilgisi.getAidiyatGuncellemeDetayListesi();
                for (AidiyatGuncellemeDetay aidiyatGuncellemeDetay : aidiyatGuncellemeListesi) {

                    GuncellemeTip guncellemeTip = aidiyatGuncellemeDetay.getGuncellemeTip();
                    String aidiyatKodu = aidiyatGuncellemeDetay.getAidiyatKodu();

                    Optional<MahkemeAidiyat> mahkemeAidiyat = dbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod(iliskiliMahkemeKarar.getId(), aidiyatKodu);
                    if (guncellemeTip == GuncellemeTip.EKLE && mahkemeAidiyat.isPresent()) {
                        throw new MakosResponseException("Zaten var");
                    } else if (guncellemeTip == GuncellemeTip.CIKAR && mahkemeAidiyat.isEmpty()) {
                        throw new MakosResponseException("Böyle bir aidiyat zaten yok");
                    }

                    MahkemeAidiyatDetayTalep talep = new MahkemeAidiyatDetayTalep();
                    talep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                    //DetayMahkemeKararTalep'de olmasina ragmen burada yine de kaydediliyor.
                    talep.setMahkemeKararTalepId(mahkemeKararTalepId);
                    talep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                    talep.setTarih(kayitTarihi);
                    if (guncellemeTip == GuncellemeTip.EKLE) {
                        talep.setMahkemeAidiyatKoduEkle(aidiyatGuncellemeDetay.getAidiyatKodu());
                    } else {
                        talep.setMahkemeAidiyatKoduCikar(aidiyatGuncellemeDetay.getAidiyatKodu());
                    }
                    MahkemeAidiyatDetayTalep savedMahkemeAidiyatDetayTalep = dbMahkemeAidiyatDetayTalepService.save(talep);
                }
            }
        }
    }

    //@Override
    @Transactional
    public Long kaydet1(IDAidiyatBilgisiGuncellemeRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {
        try {
            Long mahkemeKararTalepId = null;//mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = dbMahkemeKararTalepService.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            List<AidiyatGuncellemeKararDetay> guncellemeListesi = request.getAidiyatGuncellemeKararDetayListesi();
            if (guncellemeListesi != null) {
                for (AidiyatGuncellemeKararDetay guncellemeBilgisi : guncellemeListesi) {
                    //Güncellemeye konu mahkeme karari bul
                    MahkemeKararDetay iliskiliMahkemeKararDetayRequest = guncellemeBilgisi.getMahkemeKararDetay();
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetayRequest.getMahkemeKodu()
                            , iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetayRequest.getMahkemeKodu(), iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                        throw new MakosResponseException(errorStr);
                    }
                    MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();


                    DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
                    DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

                    List<AidiyatGuncellemeDetay> aidiyatGuncellemeListesi = guncellemeBilgisi.getAidiyatGuncellemeDetayListesi();
                    for (AidiyatGuncellemeDetay aidiyatGuncellemeDetay : aidiyatGuncellemeListesi) {

                        GuncellemeTip guncellemeTip = aidiyatGuncellemeDetay.getGuncellemeTip();
                        String aidiyatKodu = aidiyatGuncellemeDetay.getAidiyatKodu();

                        Optional<MahkemeAidiyat> mahkemeAidiyat = dbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod(iliskiliMahkemeKarar.getId(), aidiyatKodu);
                        if (guncellemeTip == GuncellemeTip.EKLE && mahkemeAidiyat.isPresent()) {
                            throw new Exception("Zaten var");
                        } else if (guncellemeTip == GuncellemeTip.CIKAR && mahkemeAidiyat.isEmpty()) {
                            throw new Exception("Böyle bir aidiyat zaten yok");
                        }

                        MahkemeAidiyatDetayTalep talep = new MahkemeAidiyatDetayTalep();
                        talep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                        //DetayMahkemeKararTalep'de olmasina ragmen burada yine de kaydediliyor.
                        talep.setMahkemeKararTalepId(mahkemeKararTalepId);
                        talep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                        talep.setTarih(kayitTarihi);
                        if (guncellemeTip == GuncellemeTip.EKLE) {
                            talep.setMahkemeAidiyatKoduEkle(aidiyatGuncellemeDetay.getAidiyatKodu());
                        } else {
                            talep.setMahkemeAidiyatKoduCikar(aidiyatGuncellemeDetay.getAidiyatKodu());
                        }
                        MahkemeAidiyatDetayTalep savedMahkemeAidiyatDetayTalep = dbMahkemeAidiyatDetayTalepService.save(talep);
                    }
                }
            }

            return mahkemeKararTalepId;
        } catch (MakosResponseException ex) {
            // Let MakosResponseException propagate without wrapping
            throw ex;
        } catch (Exception ex) {
            log.error("IDAidiyatBilgisiGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

