package iym.makos.controller;

import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.db.*;
import iym.makos.model.dto.iymdomain.*;
import iym.makos.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/iymDomainSorgu")
@Slf4j
public class IymDomainSorguController {

    private final SucTipiService sucTipiService;

    private final MahkemeKararTipiService mahkemeKararTipiService;

    private final MahkemeBilgiService mahkemeBilgiService;

    final
    TespitTurleriService tespitTurleriService;

    final
    SorguTipleriService sorguTipleriService;

    private final IllerService illerService;

    private final EvrakGelenKurumlarService evrakGelenKurumlarService;

    private final MahkemeAidiyatService mahkemeAidiyatService;

    public IymDomainSorguController(SucTipiService sucTipiService, MahkemeKararTipiService mahkemeKararTipiService, MahkemeBilgiService mahkemeBilgiService, TespitTurleriService tespitTurleriService, SorguTipleriService sorguTipleriService, IllerService illerService, EvrakGelenKurumlarService evrakGelenKurumlarService, MahkemeAidiyatService mahkemeAidiyatService) {
        this.sucTipiService = sucTipiService;
        this.mahkemeKararTipiService = mahkemeKararTipiService;
        this.mahkemeBilgiService = mahkemeBilgiService;
        this.tespitTurleriService = tespitTurleriService;
        this.sorguTipleriService = sorguTipleriService;
        this.illerService = illerService;
        this.evrakGelenKurumlarService = evrakGelenKurumlarService;
        this.mahkemeAidiyatService = mahkemeAidiyatService;
    }


    @GetMapping("sucTipleri")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<SucTipleriResponse> sucTipleri() {

        log.debug("sucTipleri sorgulama request received.");

        try {

            List<SucTipiDTO> sucTipleri = sucTipiService.findByDurum("A");
            log.info("sucTipleri sorgulandi. Size {}", sucTipleri.size());

            return ResponseEntity.ok(SucTipleriResponse.builder()
                    .sucTipleri(sucTipleri)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("sucTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(SucTipleriResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Suç Tipleri Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }

    @GetMapping("mahkemeKararTipleri")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<MahkemeKararTipleriResponse> mahkemeKararTipleri() {

        log.debug("Mahkeme Karar Tipleri Sorgulama  Request Received.");

        try {

            List<MahkemeKararTipiDTO> mahkemeKararTipleri = mahkemeKararTipiService.findAll();

            log.info("Mahkeme Karar Tipleri Sorgulandı. Size {}", mahkemeKararTipleri.size());

            return ResponseEntity.ok(MahkemeKararTipleriResponse.builder()
                    .mahkemeKararTipiListesi(mahkemeKararTipleri)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("Mahkeme Karar Tipi Sorgulama Hatası : ", ex);
            return ResponseEntity.ok(MahkemeKararTipleriResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Mahkeme Karar Tipi Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }


    @GetMapping("mahkemeKodlari")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<MahkemeKodlariResponse> mahkemeKodlari() {

        log.debug("Mahkeme Kodu Listesi Request Received.");

        try {

            List<MahkemeKoduDTO> mahkemeKoduListesi = mahkemeBilgiService.findAllMahkemeKodlari();

            log.info("Mahkeme Kodu Listesi Sorgulandı. Size {}", mahkemeKoduListesi.size());

            return ResponseEntity.ok(MahkemeKodlariResponse.builder()
                    .mahkemeKodListesi(mahkemeKoduListesi)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("sucTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(MahkemeKodlariResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Mahkeme Kodu Listesi Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }


/*
    @GetMapping("hedefTipleri")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<HedefTipleriResponse> hedefTipleri() {

        log.debug("hedefTipleri sorgulama request received.");

        try {

            List<HedefTipi> hedefTipleri = new ArrayList<>(); //
            log.info("hedefTipleri sorgulandi. Size {}", hedefTipleri.size());

            return ResponseEntity.ok(HedefTipleriResponse.builder()
                    .hedefTipleri(hedefTipleri)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage("Hedef Tipleri Sorgulama Başarılı")
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("hedefTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(HedefTipleriResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Hedef Tipleri Sorgulama Başarısız")
                            .build())
                    .build());
        }
    }
*/
    @GetMapping("sorguTipleri")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<SorguTipiListResponse> sorguTipleri() {

        log.debug("sorguTipleri sorgulama request received.");

        try {

            List<SorguTipiDTO> sorguTipleri = sorguTipleriService.findAllSorguTipleri();

            log.info("sorguTipleri sorgulandi. Size {}", sorguTipleri.size());

            return ResponseEntity.ok(SorguTipiListResponse.builder()
                    .sorguTipleri(sorguTipleri)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("sorguTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(SorguTipiListResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Sorgu Tipleri Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }

    @GetMapping("tespitTurleri")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<TespitTuruListResponse> tespitTurleri() {

        log.debug("tespitTurleri sorgulama request received.");

        try {

            List<TespitTuruDTO> tespitTurleri = tespitTurleriService.findAllTespitTurleri();

            log.info("tespitTurleri sorgulandi. Size {}", tespitTurleri.size());

            return ResponseEntity.ok(TespitTuruListResponse.builder()
                    .tespitTurleri(tespitTurleri)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("tespitTurleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(TespitTuruListResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Tespit Turleri Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }

    @GetMapping("iller")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<IllerResponse> iller() {

        log.debug("İller sorgulama request received.");

        try {

            List<IllerDTO> iller = illerService.findAll();

            log.info("İller sorgulandı. Size {}", iller.size());

            return ResponseEntity.ok(IllerResponse.builder()
                    .iller(iller)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("İller sorgulama hatası : ", ex);
            return ResponseEntity.ok(IllerResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("İller Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }

    @GetMapping("kurumlar")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<EvrakGelenKurumlarResponse> kurumlar() {

        log.debug("Kurumlar sorgulama request received.");

        try {

            List<EvrakGelenKurumlarDTO> kurumlar = evrakGelenKurumlarService.findAll();

            log.info("Kurumlar sorgulandı. Size {}", kurumlar.size());

            return ResponseEntity.ok(EvrakGelenKurumlarResponse.builder()
                    .kurumlar(kurumlar)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("Kurumlar sorgulama hatası : ", ex);
            return ResponseEntity.ok(EvrakGelenKurumlarResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Kurumlar Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }

    @GetMapping("aidiyatlar")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<AidiyatResponse> aidiyatlar() {

        log.debug("Aidiyatlar sorgulama request received.");

        try {

            List<MKTalepAidiyatDTO> aidiyatlar = mahkemeAidiyatService.findAll();

            log.info("Aidiyatlar sorgulandı. Size {}", aidiyatlar.size());

            return ResponseEntity.ok(AidiyatResponse.builder()
                    .aidiyatlar(aidiyatlar)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("Aidiyatlar sorgulama hatası : ", ex);
            return ResponseEntity.ok(AidiyatResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Aidiyatlar Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }


}
