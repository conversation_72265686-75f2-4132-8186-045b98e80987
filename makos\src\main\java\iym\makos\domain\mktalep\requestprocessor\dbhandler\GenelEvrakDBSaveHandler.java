package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.GenelEvrakRequest;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Component
@Slf4j
public class GenelEvrakDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<GenelEvrakRequest> {

    private DbMahkemeKararTalepService dbMahkemeKararTalepService;

    @Autowired
    public void GenelEvrakDBSaveHandler(DbMahkemeKararTalepService dbMahkemeKararTalepService) {
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
    }

    @Override
    @Transactional
    public void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, GenelEvrakRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {
        validateRequest(mahkemeKararTalepIdWithEvrakId, request, kayitTarihi, kullaniciId);
    }

    private void validateRequest(MahkemeKararTalepIdWithEvrakId talepAndEvrakId, MkTalepRequest request, LocalDateTime kayitTarihi, Long kullaniciId) throws DataAccessException {

        if (talepAndEvrakId == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(null, MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "mahkemeKarar/EvrakId boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        if (request == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(null, MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "request boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        if (kayitTarihi == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "kayitTarihi boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        if (kullaniciId == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "kullaniciId boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
    }

}

