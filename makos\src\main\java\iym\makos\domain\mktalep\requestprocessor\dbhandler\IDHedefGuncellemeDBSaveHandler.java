package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.*;
import iym.makos.model.dto.mktalep.request.id.IDHedefGuncellemeRequest;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IDHedefGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDHedefGuncellemeRequest> {
    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    private final DbHedeflerService dbHedeflerService;
    private final KararRequestMapper kararRequestMapper;
    private final DbHedeflerDetayTalepService dbHedeflerDetayTalepService;

    @Autowired
    public IDHedefGuncellemeDBSaveHandler(DbMahkemeKararService dbMahkemeKararService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService
            , DbHedeflerService dbHedeflerService
            , KararRequestMapper kararRequestMapper
            , DbHedeflerDetayTalepService dbHedeflerDetayTalepService
    ) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
        this.dbHedeflerService = dbHedeflerService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbHedeflerDetayTalepService = dbHedeflerDetayTalepService;
    }

    @Override
    @Transactional
    public  void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, IDHedefGuncellemeRequest request, LocalDateTime kayitTarihi, Long kullaniciId){

        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();

        for (HedefGuncellemeKararDetay hedefBilgisi : request.getHedefGuncellemeKararDetayListesi()) {

            //Güncellemeye konu mahkeme karari bul
            MahkemeKararDetay iliskiliMahkemeKararDetayRequest = hedefBilgisi.getMahkemeKararDetay();
            Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                    , iliskiliMahkemeKararDetayRequest.getMahkemeKodu()
                    , iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                    , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
            if (iliskiliMahkemeKararOpt.isEmpty()) {
                String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKodu(), iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                        , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                throw new MakosResponseException(errorStr);
            }
            MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();

            //Guncellemeye konu mahkeme karar bilgilerini detay olarak kaydet.
            DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
            DetayMahkemeKararTalep savedDetayMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

            //Her bir hedefin iliskili mahkeme kararda olup olmadigini kontrol et
            List<HedefGuncellemeDetay> hedefListesi = hedefBilgisi.getHedefGuncellemeDetayListesi();
            if (hedefListesi != null && !hedefListesi.isEmpty()) {
                for (HedefGuncellemeDetay hedefGuncellemeDetay : hedefListesi) {

                    String hedefNo = hedefGuncellemeDetay.getHedef().getHedefNo();
                    HedefTip hedefTipi = hedefGuncellemeDetay.getHedef().getHedefTip();

                    Optional<Hedefler> iliskiliHedef = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, hedefTipi.getHedefKodu());
                    if (iliskiliHedef.isEmpty()) {
                        throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                    }

                    HedeflerDetayTalep hedeflerDetayTalep = new HedeflerDetayTalep();
                    for (HedefGuncellemeBilgi hedefGuncellemeBilgisi : hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi()) {
                        if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.AD) {
                            hedeflerDetayTalep.setHedefAdi(hedefGuncellemeBilgisi.getYeniDegeri());
                        } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.SOYAD) {
                            hedeflerDetayTalep.setHedefSoyadi(hedefGuncellemeBilgisi.getYeniDegeri());
                        } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.TCKIMlIKNO) {
                            hedeflerDetayTalep.setTcKimlikNo(hedefGuncellemeBilgisi.getYeniDegeri());
                        } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.CANAK_NO) {
                            hedeflerDetayTalep.setCanakNo(hedefGuncellemeBilgisi.getYeniDegeri());
                        }
                    }

                    String updateColumnNames = hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi().stream()
                            .map(guncellemeBilgi -> guncellemeBilgi.getHedefGuncellemeAlan().name())
                            .collect(Collectors.joining(","));

                    hedeflerDetayTalep.setUpdateColumnNames(updateColumnNames);
                    //her bir hedefi guncellenecek mahkeme karar ile iliskilendir.
                    hedeflerDetayTalep.setDetayMahkemeKararTalepId(savedDetayMahkemeKararTalep.getId());
                    hedeflerDetayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);

                    HedeflerDetayTalep savedHedeflerDetayTalep = dbHedeflerDetayTalepService.save(hedeflerDetayTalep);
                    if (savedHedeflerDetayTalep == null) {
                        throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_HEDEFDETAY_KAYDETMEHATASI);
                    }
                }
            }

        }
        //return mahkemeKararTalepId;

    }
    //@Override
    @Transactional
    public Long kaydet1(IDHedefGuncellemeRequest request, LocalDateTime kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = null;//mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = dbMahkemeKararTalepService.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            for (HedefGuncellemeKararDetay hedefBilgisi : request.getHedefGuncellemeKararDetayListesi()) {

                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay iliskiliMahkemeKararDetayRequest = hedefBilgisi.getMahkemeKararDetay();
                Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                        , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                if (iliskiliMahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetayRequest.getMahkemeKodu(), iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                }
                MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();

                //Guncellemeye konu mahkeme karar bilgilerini detay olarak kaydet.
                DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
                DetayMahkemeKararTalep savedDetayMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

                //Her bir hedefin iliskili mahkeme kararda olup olmadigini kontrol et
                List<HedefGuncellemeDetay> hedefListesi = hedefBilgisi.getHedefGuncellemeDetayListesi();
                if (hedefListesi != null && !hedefListesi.isEmpty()) {
                    for (HedefGuncellemeDetay hedefGuncellemeDetay : hedefListesi) {

                        String hedefNo = hedefGuncellemeDetay.getHedef().getHedefNo();
                        HedefTip hedefTipi = hedefGuncellemeDetay.getHedef().getHedefTip();

                        Optional<Hedefler> iliskiliHedef = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, hedefTipi.getHedefKodu());
                        if (iliskiliHedef.isEmpty()) {
                            throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                        }

                        HedeflerDetayTalep hedeflerDetayTalep = new HedeflerDetayTalep();
                        for (HedefGuncellemeBilgi hedefGuncellemeBilgisi : hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi()) {
                            if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.AD) {
                                hedeflerDetayTalep.setHedefAdi(hedefGuncellemeBilgisi.getYeniDegeri());
                            } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.SOYAD) {
                                hedeflerDetayTalep.setHedefSoyadi(hedefGuncellemeBilgisi.getYeniDegeri());
                            } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.TCKIMlIKNO) {
                                hedeflerDetayTalep.setTcKimlikNo(hedefGuncellemeBilgisi.getYeniDegeri());
                            } else if (hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.CANAK_NO) {
                                hedeflerDetayTalep.setCanakNo(hedefGuncellemeBilgisi.getYeniDegeri());
                            }
                        }

                        String updateColumnNames = hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi().stream()
                                .map(guncellemeBilgi -> guncellemeBilgi.getHedefGuncellemeAlan().name())
                                .collect(Collectors.joining(","));

                        hedeflerDetayTalep.setUpdateColumnNames(updateColumnNames);
                        //her bir hedefi guncellenecek mahkeme karar ile iliskilendir.
                        hedeflerDetayTalep.setDetayMahkemeKararTalepId(savedDetayMahkemeKararTalep.getId());
                        hedeflerDetayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);

                        HedeflerDetayTalep savedHedeflerDetayTalep = dbHedeflerDetayTalepService.save(hedeflerDetayTalep);
                        if (savedHedeflerDetayTalep == null) {
                            throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_HEDEFDETAY_KAYDETMEHATASI);
                        }
                    }
                }

            }
            return mahkemeKararTalepId;
        } catch (MakosResponseException ex) {
            // Let MakosResponseException propagate without wrapping
            throw ex;
        } catch (Exception ex) {
            log.error("IDHedefGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

