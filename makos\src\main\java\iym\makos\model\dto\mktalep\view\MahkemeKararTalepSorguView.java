package iym.makos.model.dto.mktalep.view;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description =  "Mahkeme Karar Talep Listeleme  Bilgisi")
public class MahkemeKararTalepSorguView {

    @Schema(description = "Mahkeme Karar Talep Id")
    private Long mahkemeKararTalepId;

    @Schema(description = "Evrak Id")
    private Long evrakId;

    @Schema(description = "Evrak Sıra No")
    private String evrakSiraNo;

    @Schema(description = "Evrak No")
    private String evrakNo;

    @Schema(description = "Kurum Evrak No")
    private String kurumEvrakNo;

    @Schema(description = "Kurum Evrak Tarihi")
    private LocalDateTime kurumEvrakTarihi;

    @Schema(description = "<PERSON><PERSON>")
    private LocalDateTime kararKayitTarihi;

    @Schema(description = "Mahkeme İl/İlçe Kodu")
    private String mahkemeIlIlceKodu;

    @Schema(description = "Mahkeme İl/İlçe Adı")
    private String mahkemeIlIlceAdi;

    @Schema(description = "Kaydeden Kullanıcı Id")
    private Long kaydedenKullaniciId;

    @Schema(description = "Kaydeden Kullanıcı Adı")
    private String kaydedenKullaniciAdi;

    @Schema(description = "Kaydeden Adı/Soyadı")
    private String kaydedenAdiSoyadi;

    @Schema(description = "Adı")
    private String adi;

    @Schema(description = "Soyadı")
    private String soyadi;

    @Schema(description = "Soruşturma No")
    private String sorusturmaNo;

    @Schema(description = "Mahkeme Karar No")
    private String mahkemeKararNo;

    @Schema(description = "Açıklama")
    private String aciklama;

    @Schema(description = "Durumu")
    private String durumu;

    @Schema(description = "Mahkeme Kodu")
    private String mahkemeKodu;

    @Schema(description = "Mahkeme Adı")
    private String mahkemeAdi;

    @Schema(description = "Kurum Kodu")
    private String kurumKodu;

    @Schema(description = "Kurum Adı")
    private String kurumAdi;

    @Schema(description = "Evrak Konusu")
    private String evrakKonusu;


}

