package iym.makos.domain.mktaleprequest.dbhandler;


import iym.common.enums.HedefTip;
import iym.common.model.api.Hedef;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.model.entity.iym.talep.HedeflerTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.GenelEvrakDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.IDUzatmaKarariDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararRequestCommonDbSaver;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.GenelEvrakRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static iym.makos.domain.utils.TestAssertions.assertResponseSuccess;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
@DisplayName("IDUzatmaKarariDBSaveHandlerTest Unit Tests")
public class IDUzatmaKarariDBSaveHandlerTest extends BaseDomainUnitTest{

    @InjectMocks
    private IDUzatmaKarariDBSaveHandler handler;

    @Mock
    private DbMahkemeKararService dbMahkemeKararService;
    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;
    @Mock
    private DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    @Mock
    private DbHedeflerTalepService dbHedeflerTalepService;
    @Mock
    private DbHedeflerDetayTalepService dbHedeflerDetayTalepService;
    @Mock
    private DbHedeflerService dbHedeflerService;
    @Mock
    private KararRequestMapper kararRequestMapper;
    @Mock
    private MahkemeKararRequestCommonDbSaver mahkemeKararRequestCommonDbSaver;


    private MahkemeKararTalepIdWithEvrakId talepIdWithEvrakId;
    private IDUzatmaKarariRequest request;
    private LocalDateTime kayitTarihi;
    private Long kullaniciId;

    @InjectMocks
    private IDUzatmaKarariDBSaveHandler dbSaveHandler;


    @BeforeEach
    void setUp() {

        talepIdWithEvrakId = new MahkemeKararTalepIdWithEvrakId(1L, 2L);
        request = new IDUzatmaKarariRequest();

        kayitTarihi = LocalDateTime.now();
        kullaniciId = 42L;


        talepIdWithEvrakId = createTestMahkemeKararTalepIdWithEvrakId();

    }


    @Test
    void advanceHandleDbSave_nullTalep_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.advanceHandleDbSave(null, request, kayitTarihi, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("mahkemeKarar/EvrakId boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullRequest_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.advanceHandleDbSave(talepIdWithEvrakId, null, kayitTarihi, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("request boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullKayitTarihi_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.advanceHandleDbSave(talepIdWithEvrakId, request, null, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kayitTarihi boş olamaz");
    }

    @Test
    void advanceHandleDbSave_nullKullaniciId_throwsException() {
        assertThatThrownBy(() -> dbSaveHandler.advanceHandleDbSave(talepIdWithEvrakId, request, kayitTarihi, null))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kullaniciId boş olamaz");
    }



}
