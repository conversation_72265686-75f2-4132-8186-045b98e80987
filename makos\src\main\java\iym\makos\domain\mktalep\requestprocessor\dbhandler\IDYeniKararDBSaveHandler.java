package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.talep.*;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mktalep.DbHedeflerAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeSuclarTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDYeniKararDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDYeniKararRequest> {

    private final DbHedeflerTalepService dbHedeflerTalepService;
    private final DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;
    private final KararRequestMapper kararRequestMapper;
    private final DbMahkemeAidiyatTalepService dbMahkemeAidiyatTalepService;
    private final DbMahkemeSuclarTalepService dbMahkemeSuclarTalepService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;

    @Autowired
    public IDYeniKararDBSaveHandler(DbHedeflerTalepService dbHedeflerTalepService
            , DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService
            , KararRequestMapper kararRequestMapper
            , DbMahkemeAidiyatTalepService dbMahkemeAidiyatTalepService
            , DbMahkemeSuclarTalepService dbMahkemeSuclarTalepService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService) {
        this.dbHedeflerTalepService = dbHedeflerTalepService;
        this.dbHedeflerAidiyatTalepService = dbHedeflerAidiyatTalepService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbMahkemeAidiyatTalepService = dbMahkemeAidiyatTalepService;
        this.dbMahkemeSuclarTalepService = dbMahkemeSuclarTalepService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;

    }

    @Override
    @Transactional
    public void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, IDYeniKararRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {

        validateRequest(mahkemeKararTalepIdWithEvrakId, request, kayitTarihi, kullaniciId);

        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();

        // Aidiyat listesi kaydet
        saveMahkemeKararTalepAidiyatListesi(request.getMahkemeAidiyatKodlari(), mahkemeKararTalepId);

        // Suç listesi kaydet
        saveMahkemeKararTalepSucKoduListesi(request.getMahkemeSucTipiKodlari(), mahkemeKararTalepId);

        // Hedef detaylarını kaydet
        for (IDHedefDetay hedefDetay : request.getHedefDetayListesi()) {
            HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(hedefDetay, mahkemeKararTalepId, kullaniciId, kayitTarihi);

            HedeflerTalep savedHedeflerTalep = dbHedeflerTalepService.save(hedeflerTalepEntity);
            if (savedHedeflerTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_HEDEFLERTALEP_KAYDETMEHATASI, hedeflerTalepEntity.getHedefNo(), hedeflerTalepEntity.getHedefTipi());
            }

            //Hedefin aidiyat listesini kaydet.
            saveHedeflerTalepAidiyatListesi(
                    hedefDetay.getHedefAidiyatKodlari(),
                    savedHedeflerTalep.getId(),
                    savedHedeflerTalep.getHedefNo(),
                    kayitTarihi,
                    kullaniciId
            );

        }

    }

    private void validateRequest(MahkemeKararTalepIdWithEvrakId talepAndEvrakId, MkTalepRequest request, LocalDateTime kayitTarihi, Long kullaniciId) throws DataAccessException {

        if (talepAndEvrakId == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(null, MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "mahkemeKarar/EvrakId boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        if (request == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(null, MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "request boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        if (kayitTarihi == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "kayitTarihi boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }

        if (kullaniciId == null) {
            String errorStr = CommonUtils.getFormattedStringWithUUID(request.getId(), MakosResponseErrorCodes.GECERSIZ_PARAMETRE, "kullaniciId boş olamaz");
            log.error(errorStr);
            throw new MakosResponseException(errorStr);
        }
    }

    private List<HedeflerAidiyatTalep> saveHedeflerTalepAidiyatListesi(List<String> aidiyatListesi
            , Long hedeflerTalepId
            , String hedefNo
            , LocalDateTime islemTarihi
            , Long kullaniciId) {

        List<HedeflerAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : CommonUtils.safeList(aidiyatListesi)) {

            HedeflerAidiyatTalep hedeflerAidiyatTalep = HedeflerAidiyatTalep.builder()
                    .hedefTalepId(hedeflerTalepId)
                    .aidiyatKod(aidiyatKodu)
                    .tarih(islemTarihi)
                    .kullaniciId(kullaniciId)
                    .build();

            HedeflerAidiyatTalep savedHedeflerAidiyatTalep = dbHedeflerAidiyatTalepService.save(hedeflerAidiyatTalep);
            if (savedHedeflerAidiyatTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_HEDEFLER_AIDIYAT_KAYDETMEHATASI, hedefNo, aidiyatKodu);
            }
            result.add(savedHedeflerAidiyatTalep);
        }

        return result;
    }


    private List<MahkemeAidiyatTalep> saveMahkemeKararTalepAidiyatListesi(List<String> aidiyatListesi
            , Long mahkemeKararTalepId) {

        List<MahkemeAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : CommonUtils.safeList(aidiyatListesi)) {

            MahkemeAidiyatTalep mahkemeAidiyatTalep = MahkemeAidiyatTalep.builder()
                    .mahkemeKararTalepId(mahkemeKararTalepId)
                    .aidiyatKod(aidiyatKodu)
                    .build();

            MahkemeAidiyatTalep savedMahkemeAidiyatTalep = dbMahkemeAidiyatTalepService.save(mahkemeAidiyatTalep);
            if (savedMahkemeAidiyatTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_AIDIYAT_KAYDETMEHATASI, mahkemeAidiyatTalep);
            }
            result.add(savedMahkemeAidiyatTalep);
        }

        return result;
    }

    private List<MahkemeSuclarTalep> saveMahkemeKararTalepSucKoduListesi(List<String> sucKoduListesi
            , Long mahkemeKararTalepId) {

        List<MahkemeSuclarTalep> result = new ArrayList<>();

        for (String sucTipiKodu : CommonUtils.safeList(sucKoduListesi)) {

            MahkemeSuclarTalep mahkemeSuclarTalep = MahkemeSuclarTalep.builder()
                    .mahkemeKararTalepId(mahkemeKararTalepId)
                    .sucTipKodu(sucTipiKodu)
                    .build();

            MahkemeSuclarTalep savedMahkemeSuclarTalep = dbMahkemeSuclarTalepService.save(mahkemeSuclarTalep);
            if (savedMahkemeSuclarTalep == null) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_SUCTIPI_KAYDETMEHATASI, sucTipiKodu);
            }
            result.add(savedMahkemeSuclarTalep);
        }

        return result;
    }


}

