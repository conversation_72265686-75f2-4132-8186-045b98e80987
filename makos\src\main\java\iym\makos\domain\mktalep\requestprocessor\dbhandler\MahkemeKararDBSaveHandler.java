package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;

import java.time.LocalDateTime;


public interface MahkemeKararDBSaveHandler<T extends MkTalepRequest> {
    MahkemeKararTalepIdWithEvrakId kaydet(T request, LocalDateTime kayitTarihi, Long kullaniciId) ;
    Class<T> getRelatedRequestType();
}

