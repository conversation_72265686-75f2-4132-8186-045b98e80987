package iym.makos.domain.mktalep.requestprocessor.dbhandler;

import iym.common.enums.GuncellemeTip;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.mk.MahkemeSuclar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeSucTipiDetayTalep;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mk.DbMahkemeSuclarService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeSucTipiDetayTalepService;
import iym.common.util.CommonUtils;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.SucTipiGuncellemeDetay;
import iym.makos.model.api.SucTipiGuncellemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.IDSonlandirmaKarariRequest;
import iym.makos.model.dto.mktalep.request.id.IDSucTipiGuncellemeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDSucTipiGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDSucTipiGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeSuclarService dbMahkemeSuclarService;
    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final KararRequestMapper kararRequestMapper;
    private final DbMahkemeSucTipiDetayTalepService dbMahkemeSucTipiDetayTalepService;
    private final DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;


    @Autowired
    public IDSucTipiGuncellemeDBSaveHandler(DbMahkemeKararService dbMahkemeKararService
            , DbMahkemeSuclarService dbMahkemeSuclarService
            , DbMahkemeKararTalepService dbMahkemeKararTalepService
            , KararRequestMapper kararRequestMapper
            , DbMahkemeSucTipiDetayTalepService dbMahkemeSucTipiDetayTalepService
            , DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeSuclarService = dbMahkemeSuclarService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.kararRequestMapper = kararRequestMapper;
        this.dbMahkemeSucTipiDetayTalepService = dbMahkemeSucTipiDetayTalepService;
        this.dbDetayMahkemeKararTalepService = dbDetayMahkemeKararTalepService;
    }


    @Override
    @Transactional
    public void advanceHandleDbSave(MahkemeKararTalepIdWithEvrakId mahkemeKararTalepIdWithEvrakId, IDSucTipiGuncellemeRequest request, LocalDateTime kayitTarihi, Long kullaniciId) {
        Long evrakId = mahkemeKararTalepIdWithEvrakId.getEvrakId();
        Long mahkemeKararTalepId = mahkemeKararTalepIdWithEvrakId.getMahkemeKararTalepId();

        List<SucTipiGuncellemeKararDetay> guncellemeListesi = request.getSucTipiGuncellemeKararDetayListesi();

        for (SucTipiGuncellemeKararDetay guncellemeBilgisi : CommonUtils.safeList(guncellemeListesi)) {
            //Güncellemeye konu mahkeme karari bul
            MahkemeKararDetay ilgiliMahhemeKararDetay = guncellemeBilgisi.getMahkemeKararDetay();
            Optional<MahkemeKarar> mahkemeKararOpt = dbMahkemeKararService.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                    , ilgiliMahhemeKararDetay.getMahkemeKodu()
                    , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                    , ilgiliMahhemeKararDetay.getSorusturmaNo());
            if (mahkemeKararOpt.isEmpty()) {
                String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu(), ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                throw new MakosResponseException(errorStr);
            }
            MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


            DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
            DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

            for (SucTipiGuncellemeDetay sucTipiGuncellemeDetay : CommonUtils.safeList(guncellemeBilgisi.getSucTipiGuncellemeDetayListesi())) {

                GuncellemeTip guncellemeTip = sucTipiGuncellemeDetay.getGuncellemeTip();
                String sucTipiKodu = sucTipiGuncellemeDetay.getSucTipiKodu();

                Optional<MahkemeSuclar> mahkemeKararSucOpt = dbMahkemeSuclarService.findByMahkemeKararIdAndSucTipKodu(iliskiliMahkemeKarar.getId(), sucTipiKodu);
                if (guncellemeTip == GuncellemeTip.EKLE && !mahkemeKararSucOpt.isEmpty()) {
                    throw new MakosResponseException(String.format(MakosResponseErrorCodes.MK_SUCTIPI_ZATENVAR, iliskiliMahkemeKarar.getId(), sucTipiKodu));
                } else if (guncellemeTip == GuncellemeTip.CIKAR && mahkemeKararSucOpt.isEmpty()) {
                    throw new MakosResponseException(String.format(MakosResponseErrorCodes.MK_SUCTIPI_BULUNAMADI, iliskiliMahkemeKarar.getId(), sucTipiKodu));
                }

                MahkemeSucTipiDetayTalep mahkemeSucTipiDetayTalep = new MahkemeSucTipiDetayTalep();
                mahkemeSucTipiDetayTalep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                mahkemeSucTipiDetayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);
                mahkemeSucTipiDetayTalep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                mahkemeSucTipiDetayTalep.setTarih(kayitTarihi);
                if (guncellemeTip == GuncellemeTip.EKLE) {
                    mahkemeSucTipiDetayTalep.setMahkemeSucTipiKoduEkle(sucTipiKodu);
                } else {
                    mahkemeSucTipiDetayTalep.setMahkemeSucTipiKoduCikar(sucTipiKodu);
                }
                MahkemeSucTipiDetayTalep savedMahkemeAidiyatDetayTalep = dbMahkemeSucTipiDetayTalepService.save(mahkemeSucTipiDetayTalep);
            }
        }


    }

    @Transactional
    public Long kaydet1(IDSucTipiGuncellemeRequest request, LocalDateTime kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = null;//mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = dbMahkemeKararTalepService.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }
            List<SucTipiGuncellemeKararDetay> guncellemeListesi = request.getSucTipiGuncellemeKararDetayListesi();

            for (SucTipiGuncellemeKararDetay guncellemeBilgisi : CommonUtils.safeList(guncellemeListesi)) {
                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = guncellemeBilgisi.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = dbMahkemeKararService.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu(), ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                }
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


                DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId, kullaniciId, kayitTarihi);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dbDetayMahkemeKararTalepService.save(detayMahkemeKararTalep);

                for (SucTipiGuncellemeDetay sucTipiGuncellemeDetay : CommonUtils.safeList(guncellemeBilgisi.getSucTipiGuncellemeDetayListesi())) {

                    GuncellemeTip guncellemeTip = sucTipiGuncellemeDetay.getGuncellemeTip();
                    String sucTipiKodu = sucTipiGuncellemeDetay.getSucTipiKodu();

                    Optional<MahkemeSuclar> mahkemeKararSucOpt = dbMahkemeSuclarService.findByMahkemeKararIdAndSucTipKodu(iliskiliMahkemeKarar.getId(), sucTipiKodu);
                    if (guncellemeTip == GuncellemeTip.EKLE && !mahkemeKararSucOpt.isEmpty()) {
                        throw new Exception(String.format(MakosResponseErrorCodes.MK_SUCTIPI_ZATENVAR, iliskiliMahkemeKarar.getId(), sucTipiKodu));
                    } else if (guncellemeTip == GuncellemeTip.CIKAR && mahkemeKararSucOpt.isEmpty()) {
                        throw new Exception(String.format(MakosResponseErrorCodes.MK_SUCTIPI_BULUNAMADI, iliskiliMahkemeKarar.getId(), sucTipiKodu));
                    }

                    MahkemeSucTipiDetayTalep mahkemeSucTipiDetayTalep = new MahkemeSucTipiDetayTalep();
                    mahkemeSucTipiDetayTalep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                    mahkemeSucTipiDetayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);
                    mahkemeSucTipiDetayTalep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                    mahkemeSucTipiDetayTalep.setTarih(kayitTarihi);
                    if (guncellemeTip == GuncellemeTip.EKLE) {
                        mahkemeSucTipiDetayTalep.setMahkemeSucTipiKoduEkle(sucTipiKodu);
                    } else {
                        mahkemeSucTipiDetayTalep.setMahkemeSucTipiKoduCikar(sucTipiKodu);
                    }
                    MahkemeSucTipiDetayTalep savedMahkemeAidiyatDetayTalep = dbMahkemeSucTipiDetayTalepService.save(mahkemeSucTipiDetayTalep);
                }
            }


            return mahkemeKararTalepId;
        } catch (MakosResponseException ex) {
            // Let MakosResponseException propagate without wrapping
            throw ex;
        } catch (Exception ex) {
            log.error("IDAidiyatBilgisiGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

