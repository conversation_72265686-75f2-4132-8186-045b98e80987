package iym.makos.mapper;

import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguInfo;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepIslenecekView;
import iym.makos.model.dto.mktalep.view.MahkemeKararTalepSorguView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

class MahkemeKararTalepSorguMapperTest {

    private MahkemeKararTalepSorguMapper mahkemeKararTalepSorguMapper;
    private MahkemeKararTalepSorguInfo mahkemeKararTalepSorguInfo;
    private LocalDateTime testDate;

    @BeforeEach
    void setUp() {
        mahkemeKararTalepSorguMapper = new MahkemeKararTalepSorguMapper();
        testDate = LocalDateTime.now();

        mahkemeKararTalepSorguInfo = MahkemeKararTalepSorguInfo.builder()
                .id(1L)
                .evrakId(100L)
                .evrakSiraNo("E-2023-001")
                .evrakNo("2023/001")
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kaydedenKullaniciId(200L)
                .kullaniciAdi("testuser")
                .adi("Test")
                .soyadi("User")
                .mahkemeKararNo("2023/123")
                .sorusturmaNo("2023/456")
                .aciklama("Test açıklama")
                .mahkemeKodu("SC01")
                .mahkemeAdi("ANKARA 1. SULH CEZA HAKİMLİĞİ")
                .kurumKodu("K001")
                .kurumAdi("Test Kurumu")
                .evrakKonusu("Test Konusu")
                .build();
    }

    @Test
    void toKararTalepViewInfo_shouldMapEntityToDto() {
        // When
        MahkemeKararTalepIslenecekView result = mahkemeKararTalepSorguMapper.toKararTalepViewInfo(mahkemeKararTalepSorguInfo);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(mahkemeKararTalepSorguInfo.getId());
        assertThat(result.getEvrakId()).isEqualTo(mahkemeKararTalepSorguInfo.getEvrakId());
        assertThat(result.getKaydedenKullaniciId()).isEqualTo(mahkemeKararTalepSorguInfo.getKaydedenKullaniciId());
        assertThat(result.getKararKayitTarihi()).isEqualTo(mahkemeKararTalepSorguInfo.getKayitTarihi());
        assertThat(result.getDurumu()).isEqualTo(mahkemeKararTalepSorguInfo.getDurum());
        assertThat(result.getMahkemeKararNo()).isEqualTo(mahkemeKararTalepSorguInfo.getMahkemeKararNo());
        assertThat(result.getSorusturmaNo()).isEqualTo(mahkemeKararTalepSorguInfo.getSorusturmaNo());
        assertThat(result.getAciklama()).isEqualTo(mahkemeKararTalepSorguInfo.getAciklama());
    }

    @Test
    void toKararTalepViewInfo_shouldReturnNull_whenEntityIsNull() {
        // When
        MahkemeKararTalepIslenecekView result = mahkemeKararTalepSorguMapper.toKararTalepViewInfo(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toMahkemeKararTalepSorguView_shouldMapEntityToDto() {
        // When
        MahkemeKararTalepSorguView result = mahkemeKararTalepSorguMapper.toMahkemeKararTalepSorguView(mahkemeKararTalepSorguInfo);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(mahkemeKararTalepSorguInfo.getId());
        assertThat(result.getEvrakId()).isEqualTo(mahkemeKararTalepSorguInfo.getEvrakId());
        assertThat(result.getEvrakSiraNo()).isEqualTo(mahkemeKararTalepSorguInfo.getEvrakSiraNo());
        assertThat(result.getEvrakNo()).isEqualTo(mahkemeKararTalepSorguInfo.getEvrakNo());
        assertThat(result.getKararKayitTarihi()).isEqualTo(mahkemeKararTalepSorguInfo.getKayitTarihi());
        assertThat(result.getKaydedenKullaniciId()).isEqualTo(mahkemeKararTalepSorguInfo.getKaydedenKullaniciId());
        assertThat(result.getKaydedenKullaniciAdi()).isEqualTo(mahkemeKararTalepSorguInfo.getKullaniciAdi());
//        assertThat(result.getAdi()).isEqualTo(mahkemeKararTalepSorguInfo.getAdi());
//        assertThat(result.getSoyadi()).isEqualTo(mahkemeKararTalepSorguInfo.getSoyadi());
        assertThat(result.getKaydedenAdiSoyadi()).isEqualTo(mahkemeKararTalepSorguInfo.getAdi() + " " + mahkemeKararTalepSorguInfo.getSoyadi());
        assertThat(result.getDurumu()).isEqualTo(mahkemeKararTalepSorguInfo.getDurum());
        assertThat(result.getMahkemeKararNo()).isEqualTo(mahkemeKararTalepSorguInfo.getMahkemeKararNo());
        assertThat(result.getSorusturmaNo()).isEqualTo(mahkemeKararTalepSorguInfo.getSorusturmaNo());
        assertThat(result.getAciklama()).isEqualTo(mahkemeKararTalepSorguInfo.getAciklama());
        assertThat(result.getMahkemeKodu()).isEqualTo(mahkemeKararTalepSorguInfo.getMahkemeKodu());
        assertThat(result.getMahkemeAdi()).isEqualTo(mahkemeKararTalepSorguInfo.getMahkemeAdi());
        assertThat(result.getKurumKodu()).isEqualTo(mahkemeKararTalepSorguInfo.getKurumKodu());
        assertThat(result.getKurumAdi()).isEqualTo(mahkemeKararTalepSorguInfo.getKurumAdi());
        assertThat(result.getEvrakKonusu()).isEqualTo(mahkemeKararTalepSorguInfo.getEvrakKonusu());
    }

    @Test
    void toMahkemeKararTalepSorguView_shouldReturnNull_whenEntityIsNull() {
        // When
        MahkemeKararTalepSorguView result = mahkemeKararTalepSorguMapper.toMahkemeKararTalepSorguView(null);

        // Then
        assertThat(result).isNull();
    }
}