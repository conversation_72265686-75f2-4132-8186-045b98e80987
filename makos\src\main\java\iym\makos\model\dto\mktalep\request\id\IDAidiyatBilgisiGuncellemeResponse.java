package iym.makos.model.dto.mktalep.request.id;

import iym.makos.model.dto.mktalep.MkTalepResponse;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Data
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Jacksonized
public class IDAidiyatBilgisiGuncellemeResponse extends MkTalepResponse {

    @NotNull
    private Long evrakId;

    @NotNull
    private Long mahkemeKararTalepId;


}