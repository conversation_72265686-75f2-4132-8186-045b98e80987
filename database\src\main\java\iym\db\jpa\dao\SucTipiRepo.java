package iym.db.jpa.dao;

import iym.common.model.entity.iym.SucTipi;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for SucTipiRepo entity
 */
@Repository
public interface SucTipiRepo extends JpaRepository<SucTipi, String> {

    Optional<SucTipi> findBySucTipiKodu(String sucTipiKodu);

    @Query(value = "SELECT distinct ms.* FROM iym.MAHKEME_SUC_TIPLERI  ms " +
            "INNER JOIN iym.MAHKEME_SUCLAR_TALEP mst ON mst.MAHKEME_SUC_TIP_KOD = ms.SUC_TIPI " +
            "INNER JOIN iym.MAHKEME_KARAR_TALEP mt ON mt.ID = mst.MAHKEME_KARAR_ID " +
            "WHERE mt.ID  = :mahkemeKararTalepId", nativeQuery = true)
    List<SucTipi> getByMahkemeTalepId(Long mahkemeKararTalepId);

    @Query(value = "SELECT distinct ms.* FROM iym.MAHKEME_SUC_TIPLERI  ms " +
            "INNER JOIN iym.MAHKEME_SUCLAR_ISLEM mst ON mst.MAHKEME_SUC_TIP_KOD = ms.SUC_TIPI " +
            "INNER JOIN iym.MAHKEME_KARAR_ISLEM mt ON mt.ID = mst.MAHKEME_KARAR_ID " +
            "WHERE mt.ID  = :mahkemeKararIslemId", nativeQuery = true)
    List<SucTipi> getByMahkemeIslemId(Long mahkemeKararIslemId);

    @Query(value = "SELECT distinct ms.* FROM iym.MAHKEME_SUC_TIPLERI  ms " +
            "INNER JOIN iym.MAHKEME_SUCLAR mst ON mst.MAHKEME_SUC_TIP_KOD = ms.SUC_TIPI " +
            "INNER JOIN iym.MAHKEME_KARAR mt ON mt.ID = mst.MAHKEME_KARAR_ID " +
            "WHERE mt.ID  = :mahkemeKararId", nativeQuery = true)
    List<SucTipi> getByMahkemeId(Long mahkemeKararId);


    List<SucTipi> findByDurum(String durum);

    List<SucTipi> findBymahkemeKaraTipiKodu(String mahkemeKaraTipiKodu);


}
