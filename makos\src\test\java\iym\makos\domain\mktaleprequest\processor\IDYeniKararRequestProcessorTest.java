package iym.makos.domain.mktaleprequest.processor;

import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.helper.MahkemeKararTalepIdWithEvrakId;
import iym.makos.domain.mktalep.requestprocessor.processor.IDYeniKararRequestProcessor;
import iym.makos.domain.mktalep.requestprocessor.validator.IMahkemeKararRequestValidator;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.MakosUserDetails;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;

import static iym.makos.domain.testdata.TestDataBuilder.createTestMakosUser;
import static iym.makos.domain.testdata.TestDataBuilder.createValidIDYeniKararRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDYeniKararRequestProcessor.
 *
 * Tests the processor implementation for ID yeni karar requests.
 * Verifies validation, processing, and error handling scenarios.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("IDYeniKararRequestProcessor Unit Tests")
class IDYeniKararRequestProcessorTest extends BaseDomainUnitTest {
    
    @Mock
    private IMahkemeKararRequestValidator<IDYeniKararRequest> mockValidator;
    
    @Mock
    private MahkemeKararDBSaveHandler<IDYeniKararRequest> mockSaver;
    
    @InjectMocks
    private IDYeniKararRequestProcessor processor;
    
    private IDYeniKararRequest testRequest;
    private MakosUserDetails testUser;
    
    @BeforeEach
    void setUp() {
        testRequest = createValidIDYeniKararRequest();
        testUser = createTestMakosUser();
    }
    
    @Test
    @DisplayName("Should process successfully when validation passes and save succeeds")
    void shouldProcessSuccessfully_whenValidationPassesAndSaveSucceeds() throws Exception {
        // Given

        MahkemeKararTalepIdWithEvrakId expected = MahkemeKararTalepIdWithEvrakId.builder()
                .evrakId(12345L)
                .mahkemeKararTalepId(23456L)
                .build();

        ValidationResult validResult = validResult();
        when(mockValidator.validate(testRequest)).thenReturn(validResult);
        when(mockSaver.kaydet(any(IDYeniKararRequest.class), any(LocalDateTime.class), anyLong()))
                .thenReturn(expected);
        
        // When
        IDYeniKararResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseSuccess(response);
        assertThat(response.getEvrakId()).isEqualTo(expected.getEvrakId());
    }
    
    @Test
    @DisplayName("Should return invalid request response when validation fails")
    void shouldReturnInvalidRequestResponse_whenValidationFails() throws Exception {
        // Given
        ValidationResult invalidResult = new ValidationResult("Validation error");
        when(mockValidator.validate(testRequest)).thenReturn(invalidResult);
        
        // When
        IDYeniKararResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).contains("Validation error");
        assertThat(response.getEvrakId()).isNull();
    }
    
    @Test
    @DisplayName("Should return failed response when save throws exception")
    void shouldReturnFailedResponse_whenSaveThrowsException() throws Exception {
        // Given
        ValidationResult validResult = validResult();
        when(mockValidator.validate(testRequest)).thenReturn(validResult);
        when(mockSaver.kaydet(any(IDYeniKararRequest.class), any(LocalDateTime.class), anyLong()))
                .thenThrow(new RuntimeException("Database error"));
        
        // When
        IDYeniKararResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseError(response, MakosResponseCode.FAILED);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("Internal Error");
        assertThat(response.getEvrakId()).isNull();
    }
    
    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() {
        // When
        Class<IDYeniKararRequest> requestType = processor.getRelatedRequestType();
        
        // Then
        assertThat(requestType).isEqualTo(IDYeniKararRequest.class);
    }
    
    @Test
    @DisplayName("Should return correct response type")
    void shouldReturnCorrectResponseType() {
        // When
        Class<IDYeniKararResponse> responseType = processor.getRelatedResponseType();
        
        // Then
        assertThat(responseType).isEqualTo(IDYeniKararResponse.class);
    }
    
    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() {
        // When
        IDYeniKararResponse response = processor.process(null, testUser);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("Request cannot be null");
    }
    
    @Test
    @DisplayName("Should handle null user gracefully")
    void shouldHandleNullUser_gracefully() throws Exception {
        // When
        IDYeniKararResponse response = processor.process(testRequest, null);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("User cannot be null");
    }
}
